/**
 * دالة مساعدة للحصول على URL الأساسي للموقع بشكل آمن
 * تستخدم في sitemap و robots.txt و metadata
 */
export function getBaseUrl(): string {
  // تحديد URL الأساسي بناءً على بيئة التشغيل
  const baseUrl = 
    process.env.NEXT_PUBLIC_SITE_URL ||
    (process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : false) ||
    `http://localhost:${process.env.PORT || 3000}`;

  try {
    // التحقق من صحة وصيغة الـ URL
    const url = new URL(baseUrl);
    // ضمان وجود بروتوكول آمن في بيئة الإنتاج
    if (process.env.NODE_ENV === 'production' && url.protocol !== 'https:') {
      url.protocol = 'https:';
    }
    // إزالة الشرطة المائلة في النهاية لتوحيد الصيغة
    return url.toString().replace(/\/$/, '');
  } catch (error) {
    console.error('Invalid base URL provided:', baseUrl);
    // في حالة وجود خطأ، يتم إرجاع رابط نسبي آمن
    return '/';
  }
}

/**
 * دالة للحصول على URL كامل لصفحة معينة
 */
export function getFullUrl(path: string): string {
  const baseUrl = getBaseUrl()
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  return `${baseUrl}${cleanPath}`
}

/**
 * دالة للتحقق من أن URL صحيح
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}
