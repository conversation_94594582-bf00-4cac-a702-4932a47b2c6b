# 🚀 Google AdSense Integration for RibaCalc

Complete guide for integrating Google AdSense into your RibaCalc website with full compliance, optimal placement, and test mode functionality.

## 📋 Quick Start

### 1. Automated Setup (Recommended)

```bash
# Run the interactive setup script
node scripts/setup-adsense.js
```

This script will:
- ✅ Configure your environment variables
- ✅ Validate your AdSense Publisher ID
- ✅ Set up ad unit IDs
- ✅ Enable test mode for safe testing

### 2. Manual Setup

1. **Copy environment template:**
   ```bash
   cp .env.example .env.local
   ```

2. **Update AdSense configuration in `.env.local`:**
   ```bash
   # Your AdSense Publisher ID
   NEXT_PUBLIC_ADSENSE_PUBLISHER_ID=ca-pub-YOUR_ACTUAL_ID
   
   # Enable test mode for development
   NEXT_PUBLIC_ADSENSE_TEST_MODE=true
   
   # Your ad unit IDs
   NEXT_PUBLIC_ADSENSE_HEADER_SLOT=1234567890
   NEXT_PUBLIC_ADSENSE_SIDEBAR_SLOT=1234567891
   NEXT_PUBLIC_ADSENSE_CONTENT_SLOT=1234567892
   NEXT_PUBLIC_ADSENSE_MOBILE_SLOT=1234567893
   NEXT_PUBLIC_ADSENSE_INLINE_SLOT=1234567894
   ```

3. **Restart your development server:**
   ```bash
   npm run dev
   ```

## 🎯 Features

### ✅ Complete AdSense Compliance
- **Privacy Policy**: Multi-language privacy policy with AdSense disclosure
- **Terms of Service**: Legal terms covering ad display and usage
- **GDPR Compliance**: Cookie usage and data processing information
- **Mobile Responsive**: Optimized for all device sizes
- **Performance Optimized**: Lazy loading and async script loading

### ✅ Test Mode System
- **Safe Testing**: Preview ad placements without making real AdSense requests
- **Visual Placeholders**: See exactly where ads will appear
- **Toggle Control**: Switch between test and live mode instantly
- **Development Auto-Enable**: Test mode automatically enabled in development
- **Production Ready**: Easy switch to live ads for production

### ✅ Optimal Ad Placements
- **Header Banner**: Above-the-fold placement for maximum visibility
- **Sidebar Rectangle**: Desktop-optimized sidebar ads
- **Content Banner**: Strategic placement after main content
- **Mobile Sticky**: Bottom sticky ad for mobile users
- **Article Inline**: Mid-content ads for blog posts

### ✅ Multi-Language Support
- **English, French, Arabic**: Full RTL support for Arabic
- **Localized Content**: Privacy policy and terms in all languages
- **Cultural Adaptation**: Appropriate ad placements for different regions

## 🔧 Implementation Options

### Option 1: Full Site Integration

Add to your main layout (`/src/app/[lang]/layout.tsx`):

```typescript
import AdSenseLayout from '@/components/ads/AdSenseLayout';

export default async function Layout({ children, params }: LayoutProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <html lang={lang} dir={lang === 'ar' ? 'rtl' : 'ltr'}>
      <body>
        <Header dict={dict} lang={lang} />
        
        <main className="flex-1">
          <AdSenseLayout 
            lang={lang}
            showAds={true}
            adPlacements={{
              header: true,
              sidebar: true,
              content: true,
              sticky: true
            }}
          >
            {children}
          </AdSenseLayout>
        </main>
        
        <Footer dict={dict} lang={lang} />
      </body>
    </html>
  );
}
```

### Option 2: Page-Specific Integration

Add individual ads to specific components:

```typescript
import { AdSenseAd } from '@/components/ads/AdSenseAd';

export default function YourComponent() {
  return (
    <div>
      {/* Your content */}
      
      {/* Header ad */}
      <AdSenseAd 
        slot="header-banner" 
        className="mb-8" 
      />
      
      {/* Your main content */}
      
      {/* Content ad */}
      <AdSenseAd 
        slot="content-banner" 
        className="mt-8" 
      />
    </div>
  );
}
```

### Option 3: Calculator-Specific Layout

For the loan calculator page:

```typescript
import { CalculatorAdLayout } from '@/components/ads/AdSenseLayout';

export default function CalculatorPage({ dict, lang }) {
  return (
    <CalculatorAdLayout lang={lang} calculatorType="loan">
      <LoanCalculatorForm dict={dict} lang={lang} />
    </CalculatorAdLayout>
  );
}
```

## 🧪 Test Mode Usage

### Visual Test Mode

When test mode is enabled, you'll see:

```
┌─────────────────────────────────────┐
│           📱 Ad Preview             │
│        Header Banner Ad             │
│         728x90 • Desktop            │
│         320x50 • Mobile             │
└─────────────────────────────────────┘
```

### Toggle Controls

1. **Floating Button**: Bottom-right corner toggle
2. **Keyboard Shortcut**: `Ctrl + Shift + A` (or `Cmd + Shift + A` on Mac)
3. **Programmatic**: Call `toggleGlobalTestMode()` function

### Environment Control

```bash
# Enable test mode
NEXT_PUBLIC_ADSENSE_TEST_MODE=true

# Disable test mode (live ads)
NEXT_PUBLIC_ADSENSE_TEST_MODE=false
```

## 📱 Responsive Ad Behavior

### Desktop (≥768px)
- Header: 728x90 leaderboard
- Sidebar: 300x250 rectangle
- Content: 728x90 or 970x250 banner
- Inline: Responsive fluid

### Mobile (<768px)
- Header: 320x50 mobile banner
- Content: 320x100 large mobile banner
- Sticky: 320x50 bottom sticky
- Inline: 300x250 medium rectangle

## 🔒 Privacy & Compliance

### Legal Pages Created

- ✅ **Privacy Policy**: `/[lang]/privacy-policy`
  - Data collection disclosure
  - Cookie usage explanation
  - Third-party advertising notice
  - User rights information

- ✅ **Terms of Service**: `/[lang]/terms-of-service`
  - Website usage terms
  - Advertising disclaimer
  - Limitation of liability
  - Governing law

### Footer Links Added

Both English and Arabic footers now include:
- Privacy Policy link
- Terms of Service link
- Proper multi-language routing

## ⚡ Performance Optimization

### Lazy Loading
```typescript
// Ads load only when visible
const [isVisible, setIsVisible] = useState(false);

useEffect(() => {
  const observer = new IntersectionObserver(
    ([entry]) => setIsVisible(entry.isIntersecting),
    { threshold: 0.1 }
  );
  // ... observer logic
}, []);
```

### Async Script Loading
```typescript
// Non-blocking AdSense script loading
const script = document.createElement('script');
script.async = true;
script.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js';
```

### Error Handling
```typescript
// Graceful fallback for ad failures
try {
  (window.adsbygoogle = window.adsbygoogle || []).push({});
} catch (error) {
  console.error('AdSense error:', error);
  // Show fallback content or hide ad container
}
```

## 🚀 Going Live

### Pre-Launch Checklist

- [ ] AdSense account approved
- [ ] Publisher ID configured
- [ ] All ad unit IDs set up
- [ ] Privacy Policy live
- [ ] Terms of Service live
- [ ] Test mode disabled
- [ ] Mobile responsiveness tested
- [ ] Page speed optimized (>90 PageSpeed score)
- [ ] No console errors
- [ ] Analytics tracking setup

### Production Deployment

1. **Update environment variables:**
   ```bash
   NEXT_PUBLIC_ADSENSE_TEST_MODE=false
   NEXT_PUBLIC_ENABLE_ADSENSE=true
   ```

2. **Deploy to production:**
   ```bash
   npm run build
   npm run start
   ```

3. **Monitor AdSense dashboard:**
   - Check ad impressions
   - Monitor revenue
   - Watch for policy violations

## 🔍 Troubleshooting

### Common Issues

**Ads not showing:**
```bash
# Check configuration
echo $NEXT_PUBLIC_ADSENSE_PUBLISHER_ID
echo $NEXT_PUBLIC_ADSENSE_TEST_MODE

# Enable debug mode
NEXT_PUBLIC_ADSENSE_DEBUG=true
```

**Layout issues:**
- Check CSS conflicts
- Verify responsive breakpoints
- Test on different devices

**Performance issues:**
- Monitor Core Web Vitals
- Check for layout shifts
- Optimize ad loading

### Debug Mode

Enable detailed logging:
```bash
NEXT_PUBLIC_ADSENSE_DEBUG=true
```

This will log:
- Ad loading attempts
- Configuration values
- Error messages
- Performance metrics

## 📊 Analytics Integration

### Track Ad Performance

```typescript
// Google Analytics 4 events
gtag('event', 'ad_impression', {
  'ad_unit_name': 'header-banner',
  'ad_format': 'display',
  'value': 1
});

// Custom events
gtag('event', 'ad_click', {
  'ad_unit_name': 'sidebar-rectangle',
  'page_location': window.location.href
});
```

### Monitor Revenue Impact

- **Page RPM**: Revenue per 1000 page views
- **Ad CTR**: Click-through rate
- **Viewability**: Percentage of ads actually seen
- **User Experience**: Bounce rate and session duration

## 🎨 Customization

### Custom Ad Styles

```css
/* Custom ad container styling */
.adsense-container {
  margin: 2rem 0;
  padding: 1rem;
  border-radius: 8px;
  background: #f8f9fa;
}

/* Test mode styling */
.adsense-test {
  border: 2px dashed #007bff;
  background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%);
}
```

### Custom Ad Placements

```typescript
// Add new ad slot
const AD_SLOTS = {
  'custom-square': {
    slot: process.env.NEXT_PUBLIC_ADSENSE_CUSTOM_SLOT,
    format: 'rectangle',
    style: { width: '250px', height: '250px' }
  }
};
```

## 📞 Support

### AdSense Support
- [Google AdSense Help](https://support.google.com/adsense/)
- [AdSense Community](https://support.google.com/adsense/community)
- [AdSense Policies](https://support.google.com/adsense/answer/48182)

### Implementation Support
- Check component documentation in `/src/components/ads/`
- Review integration guide: `ADSENSE_INTEGRATION_GUIDE.md`
- Test with debug mode enabled
- Monitor browser console for errors

---

## 🏆 Best Practices Summary

1. **Always test first**: Use test mode before going live
2. **Monitor performance**: Keep Core Web Vitals optimal
3. **Respect users**: Don't overwhelm with ads
4. **Stay compliant**: Follow AdSense policies strictly
5. **Optimize continuously**: Test different placements and formats
6. **Mobile-first**: Ensure great mobile experience
7. **Privacy-focused**: Be transparent about data usage

**Ready to monetize your RibaCalc website with Google AdSense! 🚀**