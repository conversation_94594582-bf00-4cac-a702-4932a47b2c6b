#!/usr/bin/env node

/**
 * AdSense Setup Script for RibaCalc
 * 
 * This script helps you configure Google AdSense integration
 * by setting up environment variables and validating configuration.
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function log(message, color = 'reset') {
  console.log(colorize(message, color));
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(colorize(prompt, 'cyan'), resolve);
  });
}

function validatePublisherId(id) {
  const pattern = /^ca-pub-\d{16}$/;
  return pattern.test(id);
}

function validateSlotId(id) {
  return /^\d{10}$/.test(id);
}

async function setupAdSense() {
  log('\n🚀 AdSense Setup for RibaCalc', 'bright');
  log('=====================================\n', 'bright');
  
  log('This script will help you configure Google AdSense integration.\n', 'blue');
  
  // Check if .env.local exists
  const envPath = path.join(process.cwd(), '.env.local');
  const envExamplePath = path.join(process.cwd(), '.env.example');
  
  if (!fs.existsSync(envExamplePath)) {
    log('❌ .env.example file not found. Please run this script from the project root.', 'red');
    process.exit(1);
  }
  
  let envContent = '';
  if (fs.existsSync(envPath)) {
    log('📄 Found existing .env.local file', 'yellow');
    const overwrite = await question('Do you want to update it? (y/n): ');
    if (overwrite.toLowerCase() !== 'y') {
      log('Setup cancelled.', 'yellow');
      rl.close();
      return;
    }
    envContent = fs.readFileSync(envPath, 'utf8');
  } else {
    log('📄 Creating new .env.local file', 'green');
    envContent = fs.readFileSync(envExamplePath, 'utf8');
  }
  
  log('\n📋 Please provide your AdSense configuration:', 'bright');
  
  // Get Publisher ID
  let publisherId;
  while (true) {
    publisherId = await question('\n🔑 Enter your AdSense Publisher ID (ca-pub-XXXXXXXXXXXXXXXXX): ');
    if (validatePublisherId(publisherId)) {
      break;
    }
    log('❌ Invalid format. Publisher ID should be like: ca-pub-****************', 'red');
  }
  
  // Get Ad Unit IDs
  log('\n📱 Now enter your Ad Unit IDs (you can create these in your AdSense account):', 'blue');
  
  const adSlots = {
    header: await question('Header Banner Ad Unit ID (10 digits): '),
    sidebar: await question('Sidebar Rectangle Ad Unit ID (10 digits): '),
    content: await question('Content Banner Ad Unit ID (10 digits): '),
    mobile: await question('Mobile Sticky Ad Unit ID (10 digits): '),
    inline: await question('Article Inline Ad Unit ID (10 digits): ')
  };
  
  // Validate ad slot IDs
  for (const [key, value] of Object.entries(adSlots)) {
    if (value && !validateSlotId(value)) {
      log(`⚠️  Warning: ${key} ad unit ID "${value}" doesn't match expected format (10 digits)`, 'yellow');
    }
  }
  
  // Test mode setting
  const testMode = await question('\n🧪 Enable test mode? (y/n) [recommended for initial setup]: ');
  const enableTestMode = testMode.toLowerCase() === 'y';
  
  // Update environment content
  envContent = envContent.replace(
    /NEXT_PUBLIC_ADSENSE_PUBLISHER_ID=.*/,
    `NEXT_PUBLIC_ADSENSE_PUBLISHER_ID=${publisherId}`
  );
  
  envContent = envContent.replace(
    /NEXT_PUBLIC_ADSENSE_TEST_MODE=.*/,
    `NEXT_PUBLIC_ADSENSE_TEST_MODE=${enableTestMode}`
  );
  
  if (adSlots.header) {
    envContent = envContent.replace(
      /NEXT_PUBLIC_ADSENSE_HEADER_SLOT=.*/,
      `NEXT_PUBLIC_ADSENSE_HEADER_SLOT=${adSlots.header}`
    );
  }
  
  if (adSlots.sidebar) {
    envContent = envContent.replace(
      /NEXT_PUBLIC_ADSENSE_SIDEBAR_SLOT=.*/,
      `NEXT_PUBLIC_ADSENSE_SIDEBAR_SLOT=${adSlots.sidebar}`
    );
  }
  
  if (adSlots.content) {
    envContent = envContent.replace(
      /NEXT_PUBLIC_ADSENSE_CONTENT_SLOT=.*/,
      `NEXT_PUBLIC_ADSENSE_CONTENT_SLOT=${adSlots.content}`
    );
  }
  
  if (adSlots.mobile) {
    envContent = envContent.replace(
      /NEXT_PUBLIC_ADSENSE_MOBILE_SLOT=.*/,
      `NEXT_PUBLIC_ADSENSE_MOBILE_SLOT=${adSlots.mobile}`
    );
  }
  
  if (adSlots.inline) {
    envContent = envContent.replace(
      /NEXT_PUBLIC_ADSENSE_INLINE_SLOT=.*/,
      `NEXT_PUBLIC_ADSENSE_INLINE_SLOT=${adSlots.inline}`
    );
  }
  
  // Write the file
  fs.writeFileSync(envPath, envContent);
  
  log('\n✅ Configuration saved successfully!', 'green');
  log('\n📋 Next Steps:', 'bright');
  log('1. Restart your development server', 'blue');
  log('2. Visit your website to see ad placeholders (test mode)', 'blue');
  log('3. Use the floating toggle button to switch between test/live mode', 'blue');
  log('4. When ready for production, set NEXT_PUBLIC_ADSENSE_TEST_MODE=false', 'blue');
  
  if (enableTestMode) {
    log('\n🧪 Test mode is enabled - you\'ll see placeholder ads', 'yellow');
    log('Toggle the test mode button on your website to switch to live ads', 'yellow');
  } else {
    log('\n🔴 Live mode is enabled - real AdSense ads will be displayed', 'red');
    log('Make sure your AdSense account is approved and ad units are active', 'red');
  }
  
  log('\n📖 For detailed integration instructions, see:', 'cyan');
  log('   ./ADSENSE_INTEGRATION_GUIDE.md', 'cyan');
  
  rl.close();
}

// Handle errors
process.on('unhandledRejection', (error) => {
  log(`\n❌ Error: ${error.message}`, 'red');
  rl.close();
  process.exit(1);
});

process.on('SIGINT', () => {
  log('\n\n👋 Setup cancelled by user', 'yellow');
  rl.close();
  process.exit(0);
});

// Run the setup
setupAdSense().catch((error) => {
  log(`\n❌ Setup failed: ${error.message}`, 'red');
  process.exit(1);
});