"use client";

/**
 * مكون Schema Markup المحسن لتحسين SEO
 * Enhanced Schema Markup component for SEO optimization
 */

import { useMemo, memo } from 'react';
import type { Locale } from '@/types';

/**
 * أنواع Schema المدعومة
 * Supported schema types
 */
type SchemaType = 
  | 'organization' 
  | 'website' 
  | 'breadcrumb' 
  | 'calculator' 
  | 'financialService'
  | 'faq'
  | 'article'
  | 'howTo';

/**
 * خصائص مكون Schema Markup
 * Schema Markup component props
 */
interface SchemaMarkupProps {
  readonly type: SchemaType;
  readonly data?: any;
  readonly lang?: Locale;
  readonly baseUrl?: string;
}

/**
 * بيانات الفتات للتنقل
 * Breadcrumb data interface
 */
interface BreadcrumbItem {
  readonly name: string;
  readonly url: string;
}

/**
 * بيانات الأسئلة الشائعة
 * FAQ data interface
 */
interface FAQItem {
  readonly question: string;
  readonly answer: string;
}

/**
 * مكون Schema Markup المحسن
 * Enhanced Schema Markup component
 */
export const SchemaMarkup = memo(function SchemaMarkup({ 
  type, 
  data, 
  lang = 'en',
  baseUrl = 'https://calc.tolabi.net'
}: SchemaMarkupProps) {
  /**
   * إنشاء بيانات Schema
   * Generate schema data
   */
  const generatedSchema = useMemo(() => {
    const currentYear = new Date().getFullYear();
    const siteName = lang === 'ar' ? "حساب الفائدة (الربا)" : "RibaCalc";
    
    switch (type) {
      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": siteName,
          "description": lang === 'en'
            ? "Free Morocco loan calculator for MAD loans. Calculate fixed interest, monthly payments, and total repayment with Islamic banking awareness."
            : lang === 'fr'
            ? "Calculateur de prêt gratuit pour le Maroc en MAD. Calculez les intérêts fixes, paiements mensuels et remboursement total avec sensibilisation bancaire islamique."
            : "حاسبة قروض مجانية للمغرب بالدرهم المغربي. احسب الفوائد الثابتة والدفعات الشهرية والسداد الإجمالي مع الوعي المصرفي الإسلامي.",
          "url": baseUrl,
          "logo": {
            "@type": "ImageObject",
            "url": `${baseUrl}/logo.png`,
            "width": 512,
            "height": 512
          },
          "sameAs": [
            // يمكن إضافة روابط وسائل التواصل الاجتماعي هنا
            // "https://twitter.com/ribacalc",
            // "https://linkedin.com/company/ribacalc"
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["English", "French", "Arabic"],
            "areaServed": "Morocco"
          },
          "areaServed": {
            "@type": "Country",
            "name": "Morocco",
            "alternateName": ["المغرب", "Maroc"]
          },
          "knowsAbout": [
            "Loan Calculations",
            "Islamic Banking",
            "Morocco Banking",
            "Interest Calculations",
            "Financial Planning",
            "MAD Loans",
            "Shariah Compliance"
          ],
          "foundingDate": "2025-01-01",
          "legalName": siteName
        };

      case 'website':
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": siteName,
          "alternateName": lang === 'ar' ? ["حاسبة القروض", "حساب الربا"] : ["Loan Calculator", "Interest Calculator"],
          "description": lang === 'en'
            ? "Morocco's most trusted loan calculator for MAD loans, Islamic banking guidance, and comprehensive financial planning tools."
            : lang === 'fr'
            ? "Calculateur de prêt le plus fiable du Maroc pour les prêts MAD, les conseils bancaires islamiques et les outils complets de planification financière."
            : "حاسبة القروض الأكثر موثوقية في المغرب للقروض بالدرهم المغربي وإرشادات البنوك الإسلامية وأدوات التخطيط المالي الشاملة.",
          "url": baseUrl,
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${baseUrl}/${lang}/blog?search={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          },
          "inLanguage": lang === 'en' ? "en-US" : lang === 'fr' ? "fr-FR" : "ar-MA",
          "isAccessibleForFree": true,
          "copyrightYear": currentYear,
          "copyrightHolder": {
            "@type": "Organization",
            "name": siteName
          },
          "publisher": {
            "@type": "Organization",
            "name": siteName,
            "url": baseUrl
          }
        };

      case 'calculator':
        return {
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": lang === 'ar' ? "حساب الفائدة (الربا) - حاسبة القروض المغرب" : "RibaCalc - Morocco Loan Calculator",
          "alternateName": lang === 'ar' ? "حاسبة القروض المغربية" : "Morocco Loan Calculator",
          "description": lang === 'en'
            ? "Free, accurate online calculator for Morocco loans in MAD. Calculate fixed interest, monthly payments, total repayment with Islamic banking compliance warnings."
            : lang === 'fr'
            ? "Calculateur en ligne gratuit et précis pour les prêts Maroc en MAD. Calculez les intérêts fixes, les paiements mensuels, le remboursement total avec des avertissements de conformité bancaire islamique."
            : "حاسبة مجانية ودقيقة عبر الإنترنت لقروض المغرب بالدرهم المغربي. احسب الفوائد الثابتة والدفعات الشهرية والسداد الإجمالي مع تحذيرات الامتثال المصرفي الإسلامي.",
          "url": `${baseUrl}/${lang}`,
          "applicationCategory": "FinanceApplication",
          "operatingSystem": "Web Browser",
          "browserRequirements": "Requires JavaScript",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "MAD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": [
            "Fixed Interest Rate Calculation",
            "Monthly Payment Calculation", 
            "Total Repayment Amount Calculation",
            "MAD Currency Support",
            "Islamic Banking Compliance Warnings",
            "Multi-language Support (Arabic, French, English)",
            "Responsive Design",
            "Real-time Calculations",
            "Export Results"
          ],
          "softwareVersion": "1.0.0",
          "datePublished": "2025-01-01",
          "dateModified": new Date().toISOString().split('T')[0],
          "author": {
            "@type": "Organization",
            "name": siteName
          },
          "maintainer": {
            "@type": "Organization", 
            "name": siteName
          },
          "isAccessibleForFree": true,
          "screenshot": {
            "@type": "ImageObject",
            "url": `${baseUrl}/screenshot.png`,
            "description": "Screenshot of the loan calculator interface"
          }
        };

      case 'financialService':
        return {
          "@context": "https://schema.org",
          "@type": "FinancialService",
          "name": lang === 'ar' ? "خدمة حساب الفائدة (الربا) للقروض" : "RibaCalc Loan Calculator Service",
          "description": lang === 'en'
            ? "Professional loan calculation services for Morocco residents. Free, accurate, and Shariah-compliant calculations with comprehensive financial guidance."
            : lang === 'fr'
            ? "Services de calcul de prêt professionnels pour les résidents du Maroc. Calculs gratuits, précis et conformes à la Charia avec des conseils financiers complets."
            : "خدمات حساب القروض المهنية لسكان المغرب. حسابات مجانية ودقيقة ومتوافقة مع الشريعة مع إرشادات مالية شاملة.",
          "url": `${baseUrl}/${lang}`,
          "areaServed": {
            "@type": "Country",
            "name": "Morocco"
          },
          "serviceType": "Loan Calculation and Financial Advisory",
          "provider": {
            "@type": "Organization",
            "name": siteName
          },
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Financial Calculation Services",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Personal Loan Calculator",
                  "description": "Calculate personal loan payments, interest and total cost"
                },
                "price": "0",
                "priceCurrency": "MAD"
              },
              {
                "@type": "Offer", 
                "itemOffered": {
                  "@type": "Service",
                  "name": "Mortgage Calculator",
                  "description": "Calculate home loan payments, interest and amortization"
                },
                "price": "0",
                "priceCurrency": "MAD"
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Islamic Banking Consultation",
                  "description": "Shariah-compliant alternatives and guidance"
                },
                "price": "0",
                "priceCurrency": "MAD"
              }
            ]
          },
          "isRelatedTo": [
            "Banking",
            "Finance",
            "Islamic Banking",
            "Loans",
            "Morocco"
          ]
        };

      case 'breadcrumb':
        return {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": (data?.breadcrumbs as BreadcrumbItem[] || []).map((item, index) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": item.name,
            "item": `${baseUrl}${item.url}`
          }))
        };

      case 'faq':
        return {
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": (data?.faqs as FAQItem[] || []).map((faq) => ({
            "@type": "Question",
            "name": faq.question,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": faq.answer
            }
          }))
        };

      case 'article':
        return {
          "@context": "https://schema.org",
          "@type": "Article",
          "headline": data?.title || siteName,
          "description": data?.description || "",
          "author": {
            "@type": "Organization",
            "name": siteName
          },
          "publisher": {
            "@type": "Organization",
            "name": siteName,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/logo.png`
            }
          },
          "datePublished": data?.publishedDate || new Date().toISOString(),
          "dateModified": data?.modifiedDate || new Date().toISOString(),
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": data?.url || baseUrl
          },
          "image": data?.image ? {
            "@type": "ImageObject",
            "url": data.image,
            "width": 1200,
            "height": 630
          } : undefined
        };

      case 'howTo':
        return {
          "@context": "https://schema.org",
          "@type": "HowTo",
          "name": data?.title || "How to Calculate Loan Interest",
          "description": data?.description || "Step-by-step guide to calculate loan interest and monthly payments",
          "step": data?.steps?.map((step: any, index: number) => ({
            "@type": "HowToStep",
            "position": index + 1,
            "name": step.name,
            "text": step.description,
            "image": step.image ? {
              "@type": "ImageObject",
              "url": step.image
            } : undefined
          })) || []
        };

      default:
        return null;
    }
  }, [type, data, lang, baseUrl]);

  if (!generatedSchema) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(generatedSchema, null, 0) }}
    />
  );
});

/**
 * مكون متعدد Schema Markup
 * Multiple Schema Markup component
 */
export const MultipleSchemaMarkup = memo(function MultipleSchemaMarkup({ 
  schemas 
}: { 
  readonly schemas: SchemaMarkupProps[] 
}) {
  return (
    <>
      {schemas.map((schema, index) => (
        <SchemaMarkup key={`${schema.type}-${index}`} {...schema} />
      ))}
    </>
  );
});

/**
 * تصدير الأنواع
 * Export types
 */
export type { SchemaMarkupProps, SchemaType, BreadcrumbItem, FAQItem };
