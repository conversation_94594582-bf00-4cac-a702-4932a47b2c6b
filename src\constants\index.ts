/**
 * الثوابت والمتغيرات المشتركة للمشروع
 * Shared constants and variables for the project
 */

import type { Locale } from '@/types';

// اللغات المدعومة
export const SUPPORTED_LOCALES: readonly Locale[] = ['en', 'fr', 'ar'] as const;
export const DEFAULT_LOCALE: Locale = 'en';

// إعدادات حاسبة القرض
export const LOAN_CALCULATOR_SETTINGS = {
  MIN_LOAN_AMOUNT: 1,
  MAX_LOAN_AMOUNT: 10_000_000,
  MIN_INTEREST_RATE: 0,
  MAX_INTEREST_RATE: 100,
  MIN_LOAN_YEARS: 1,
  MAX_LOAN_YEARS: 50,
  CALCULATION_DELAY_MS: 700,
} as const;

// إعدادات العملات
export const CURRENCY_SETTINGS = {
  DEFAULT_CURRENCY: 'MAD',
  FALLBACK_CURRENCY: 'USD',
  DECIMAL_PLACES: 2,
} as const;

// أكواد العملات العربية
export const ARAB_CURRENCY_CODES = [
  'AED', 'SAR', 'EGP', 'KWD', 'QAR', 'BHD', 'OMR', 
  'JOD', 'MAD', 'TND', 'DZD', 'LBP', 'IQD', 'SYP', 
  'YER', 'LYD', 'SDG'
] as const;

// إعدادات الأمان
export const SECURITY_SETTINGS = {
  CSP_NONCE_LENGTH: 32,
  SESSION_TIMEOUT_MINUTES: 30,
  MAX_REQUEST_SIZE: '10mb',
  RATE_LIMIT_REQUESTS: 100,
  RATE_LIMIT_WINDOW_MS: 15 * 60 * 1000, // 15 دقيقة
} as const;

// إعدادات الأداء
export const PERFORMANCE_SETTINGS = {
  CACHE_DURATION_SECONDS: 3600, // ساعة واحدة
  IMAGE_QUALITY: 85,
  COMPRESSION_LEVEL: 6,
  PREFETCH_TIMEOUT_MS: 5000,
} as const;

// مسارات API
export const API_ROUTES = {
  CALCULATOR: '/api/calculator',
  CURRENCIES: '/api/currencies',
  DICTIONARIES: '/api/dictionaries',
  HEALTH: '/api/health',
} as const;

// رسائل الخطأ الافتراضية
export const ERROR_MESSAGES = {
  INVALID_INPUT: 'Invalid input provided',
  CALCULATION_ERROR: 'Error occurred during calculation',
  CURRENCY_NOT_FOUND: 'Currency not found',
  LOCALE_NOT_SUPPORTED: 'Locale not supported',
  NETWORK_ERROR: 'Network error occurred',
  TIMEOUT_ERROR: 'Request timeout',
} as const;

// إعدادات SEO
export const SEO_SETTINGS = {
  SITE_NAME: 'RibaCalc',
  SITE_DESCRIPTION: 'حاسبة القروض مع التحذير من مخاطر الربا - Loan Calculator with Riba Warning',
  SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://ribacalc.com',
  TWITTER_HANDLE: '@ribacalc',
  DEFAULT_OG_IMAGE: '/images/og-default.jpg',
} as const;

// تعبيرات نمطية للتحقق من صحة البيانات
export const VALIDATION_PATTERNS = {
  CURRENCY_CODE: /^[A-Z]{3}$/,
  LOCALE_CODE: /^[a-z]{2}$/,
  POSITIVE_NUMBER: /^\d*\.?\d+$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/.+/,
} as const;

// إعدادات الرسوم البيانية
export const CHART_SETTINGS = {
  COLORS: {
    PRIMARY: 'hsl(var(--primary))',
    DANGER: '#dc2626',
    SUCCESS: '#16a34a',
    WARNING: '#ca8a04',
    INFO: '#0ea5e9',
  },
  ANIMATION_DURATION: 1000,
  INNER_RADIUS: 60,
  OUTER_RADIUS: 80,
  PADDING_ANGLE: 5,
} as const;

// إعدادات التخزين المحلي
export const STORAGE_KEYS = {
  SELECTED_CURRENCY: 'selected_currency',
  PREFERRED_LOCALE: 'preferred_locale',
  CALCULATION_HISTORY: 'calculation_history',
  USER_PREFERENCES: 'user_preferences',
} as const;

// إعدادات الشبكة الاجتماعية
export const SOCIAL_MEDIA = {
  FACEBOOK_APP_ID: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID || '',
  TWITTER_SITE: '@ribacalc',
  LINKEDIN_COMPANY: 'ribacalc',
} as const;

// إعدادات الإعلانات
export const ADSENSE_SETTINGS = {
  CLIENT_ID: process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID || '',
  SLOT_IDS: {
    HEADER: process.env.NEXT_PUBLIC_ADSENSE_HEADER_SLOT || '',
    SIDEBAR: process.env.NEXT_PUBLIC_ADSENSE_SIDEBAR_SLOT || '',
    FOOTER: process.env.NEXT_PUBLIC_ADSENSE_FOOTER_SLOT || '',
  },
} as const;

// رموز HTTP للحالات
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

// إعدادات Cookie
export const COOKIE_SETTINGS = {
  SECURE: process.env.NODE_ENV === 'production',
  HTTP_ONLY: true,
  SAME_SITE: 'strict' as const,
  MAX_AGE: 30 * 24 * 60 * 60, // 30 يوم
  PATH: '/',
} as const;