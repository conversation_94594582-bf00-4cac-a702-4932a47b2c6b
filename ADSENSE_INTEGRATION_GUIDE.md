# AdSense Integration Guide for RibaCalc

This guide provides complete instructions for integrating Google AdSense into your RibaCalc website while ensuring full compliance with AdSense policies.

## 🎯 Overview

The integration includes:
- **Legal Compliance**: Privacy Policy and Terms of Service pages
- **Test Mode System**: Preview ad placements before going live
- **Optimal Ad Placements**: Following Google's best practices
- **Responsive Design**: Mobile and desktop optimized
- **Multi-language Support**: English, French, and Arabic

## 📋 Prerequisites for AdSense Approval

### ✅ Content Requirements
- [x] High-quality, original content (your calculator and blog)
- [x] Clear navigation structure
- [x] Privacy Policy page (✅ Created)
- [x] Terms of Service page (✅ Created)
- [x] Mobile-friendly responsive design
- [x] Fast loading times
- [x] No copyright violations

### ✅ Technical Requirements
- [x] HTTPS enabled
- [x] Valid HTML structure
- [x] Proper meta tags and SEO
- [x] Sitemap.xml (already exists)
- [x] Robots.txt (already exists)

## 🚀 Implementation Steps

### Step 1: Apply for AdSense Account

1. Visit [Google AdSense](https://www.google.com/adsense/)
2. Sign up with your Google account
3. Add your website: `https://calc.tolabi.net`
4. Wait for approval (typically 1-14 days)

### Step 2: Configure AdSense Settings

Once approved, update the AdSense configuration in `/src/components/ads/AdSenseAd.tsx`:

```typescript
const ADSENSE_CONFIG = {
  publisherId: 'ca-pub-YOUR_ACTUAL_PUBLISHER_ID', // Replace with your ID
  testMode: process.env.NODE_ENV === 'development',
};

const AD_SLOTS = {
  'header-banner': {
    slot: 'YOUR_HEADER_SLOT_ID', // Replace with actual slot ID
    // ... other config
  },
  // Update all slot IDs with your actual AdSense ad unit IDs
};
```

### Step 3: Integrate AdSense into Your Pages

#### Option A: Full Layout Integration (Recommended)

Update your main layout file `/src/app/[lang]/layout.tsx`:

```typescript
import AdSenseLayout from '@/components/ads/AdSenseLayout';

export default async function Layout({ children, params }: LayoutProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <html lang={lang} dir={lang === 'ar' ? 'rtl' : 'ltr'}>
      <head>
        {/* Existing head content */}
      </head>
      <body className={`antialiased font-body ${lang === 'ar' ? 'font-arabic' : ''}`}>
        <Header dict={dict} lang={lang} />
        
        <main className="flex-1">
          <AdSenseLayout 
            lang={lang}
            showAds={true}
            adPlacements={{
              header: true,
              sidebar: true,
              content: true,
              sticky: true
            }}
          >
            {children}
          </AdSenseLayout>
        </main>
        
        {lang === 'ar' ? (
          <FooterArabic dict={dict} />
        ) : (
          <Footer dict={dict} lang={lang} />
        )}
        
        <Toaster />
        <MultipleSchemaMarkup type="website" lang={lang} />
      </body>
    </html>
  );
}
```

#### Option B: Page-Specific Integration

For the calculator page `/src/components/EnhancedHomepage.tsx`:

```typescript
import { CalculatorAdLayout, InlineAd } from '@/components/ads/AdSenseLayout';

export default function EnhancedHomepage({ dict, lang }: EnhancedHomepageProps) {
  return (
    <CalculatorAdLayout lang={lang} calculatorType="loan">
      <div className={`min-h-screen ${lang === 'ar' ? 'font-arabic' : ''}`}>
        {/* Hero Section */}
        <div className="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-primary/10">
          {/* Existing hero content */}
        </div>

        {/* Calculator Section */}
        <section id="calculator" className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <LoanCalculatorForm dict={dict} lang={lang} />
          </div>
        </section>

        {/* Inline Ad after calculator */}
        <InlineAd lang={lang} className="container mx-auto px-4" />

        {/* Features Section */}
        <section className="py-16 bg-muted/30">
          {/* Existing features content */}
        </section>

        {/* Rest of your content */}
      </div>
    </CalculatorAdLayout>
  );
}
```

#### Option C: Blog/Article Integration

For blog posts, use the `ArticleAdLayout`:

```typescript
import { ArticleAdLayout, InlineAd } from '@/components/ads/AdSenseLayout';

export default function BlogPost({ post, lang }) {
  return (
    <ArticleAdLayout 
      lang={lang} 
      contentLength={post.content.length > 2000 ? 'long' : 'medium'}
    >
      <article>
        <h1>{post.title}</h1>
        
        {/* First few paragraphs */}
        <div dangerouslySetInnerHTML={{ __html: post.content.slice(0, 1000) }} />
        
        {/* Inline ad after first section */}
        <InlineAd lang={lang} />
        
        {/* Rest of content */}
        <div dangerouslySetInnerHTML={{ __html: post.content.slice(1000) }} />
      </article>
    </ArticleAdLayout>
  );
}
```

### Step 4: Test Mode Usage

#### Enable Test Mode

1. **Development**: Test mode is automatically enabled
2. **Production**: Use the floating toggle button (bottom-right)
3. **Programmatic**: Call `toggleGlobalTestMode()` function

#### Test Mode Features

- ✅ Shows placeholder boxes with "Ad Preview" text
- ✅ Displays ad placement information (size, position)
- ✅ No requests made to AdSense servers
- ✅ Perfect for testing layouts and positions
- ✅ Toggle between test and live mode instantly

### Step 5: Environment Configuration

Add to your `.env.local` file:

```bash
# AdSense Configuration
NEXT_PUBLIC_ADSENSE_PUBLISHER_ID=ca-pub-YOUR_PUBLISHER_ID
NEXT_PUBLIC_ADSENSE_TEST_MODE=false
```

Update the AdSense component to use environment variables:

```typescript
const ADSENSE_CONFIG = {
  publisherId: process.env.NEXT_PUBLIC_ADSENSE_PUBLISHER_ID || 'ca-pub-XXXXXXXXXXXXXXXXX',
  testMode: process.env.NEXT_PUBLIC_ADSENSE_TEST_MODE === 'true' || process.env.NODE_ENV === 'development',
};
```

## 📱 Ad Placement Strategy

### Desktop Layout
```
┌─────────────────────────────────────┐
│           Header Banner Ad          │ ← Above fold
├─────────────────────┬───────────────┤
│                     │               │
│    Main Content     │   Sidebar Ad  │ ← Sticky rectangle
│                     │               │
│                     │               │
├─────────────────────┴───────────────┤
│          Content Banner Ad          │ ← After main content
└─────────────────────────────────────┘
```

### Mobile Layout
```
┌─────────────────────────────────────┐
│           Header Banner Ad          │ ← Above fold
├─────────────────────────────────────┤
│                                     │
│            Main Content             │
│                                     │
├─────────────────────────────────────┤
│          Inline Article Ad          │ ← Mid-content
├─────────────────────────────────────┤
│            More Content             │
├─────────────────────────────────────┤
│          Content Banner Ad          │ ← After content
├─────────────────────────────────────┤
│         Sticky Bottom Ad            │ ← Fixed bottom
└─────────────────────────────────────┘
```

## 🔧 Customization Options

### Ad Placement Control

```typescript
// Disable specific ad placements
<AdSenseLayout 
  lang={lang}
  adPlacements={{
    header: true,     // Top banner
    sidebar: false,   // Disable sidebar ads
    content: true,    // Bottom content ad
    sticky: false     // Disable mobile sticky
  }}
>
```

### Conditional Ad Display

```typescript
// Show ads only on specific pages
const showAds = !pathname.includes('/privacy-policy') && !pathname.includes('/terms-of-service');

<AdSenseLayout showAds={showAds} lang={lang}>
```

### Custom Ad Slots

Add new ad slots in `AdSenseAd.tsx`:

```typescript
const AD_SLOTS = {
  // Existing slots...
  'custom-square': {
    slot: 'YOUR_CUSTOM_SLOT_ID',
    format: 'rectangle',
    responsive: true,
    style: { display: 'block', width: '250px', height: '250px' },
    testStyle: { width: '250px', height: '250px' }
  }
};
```

## 🚨 Important Compliance Notes

### AdSense Policy Compliance

1. **Content Quality**: Ensure all content is original and valuable
2. **User Experience**: Ads should not interfere with navigation
3. **Click Fraud**: Never encourage users to click ads
4. **Content Restrictions**: No adult, violent, or illegal content
5. **Privacy**: Privacy Policy must mention Google AdSense

### GDPR/Privacy Compliance

- ✅ Privacy Policy includes cookie usage
- ✅ Terms of Service covers ad display
- ✅ No personal data collection without consent
- ✅ Clear information about third-party ads

### Performance Optimization

1. **Lazy Loading**: Ads load only when visible
2. **Async Loading**: Non-blocking ad script loading
3. **Error Handling**: Graceful fallbacks for ad failures
4. **Mobile Optimization**: Responsive ad units

## 📊 Monitoring and Analytics

### AdSense Performance

1. Monitor in AdSense dashboard:
   - Page RPM (Revenue per 1000 impressions)
   - CTR (Click-through rate)
   - CPC (Cost per click)
   - Viewability rates

2. Optimize based on data:
   - Test different ad placements
   - Adjust ad sizes
   - Monitor user experience metrics

### Google Analytics Integration

Add to your analytics to track ad performance impact:

```javascript
// Track ad visibility
gtag('event', 'ad_impression', {
  'ad_unit_name': 'header-banner',
  'value': 1
});
```

## 🔍 Troubleshooting

### Common Issues

1. **Ads not showing**:
   - Check publisher ID is correct
   - Verify ad slot IDs
   - Ensure test mode is disabled
   - Check for ad blockers

2. **Layout issues**:
   - Test responsive behavior
   - Check CSS conflicts
   - Verify ad container sizes

3. **Performance issues**:
   - Monitor Core Web Vitals
   - Optimize ad loading
   - Check for layout shifts

### Debug Mode

Enable debug logging:

```typescript
// Add to AdSenseAd.tsx
const DEBUG = process.env.NODE_ENV === 'development';

if (DEBUG) {
  console.log('AdSense Debug:', {
    slot,
    testMode,
    publisherId: ADSENSE_CONFIG.publisherId
  });
}
```

## 🎉 Go Live Checklist

- [ ] AdSense account approved
- [ ] Publisher ID updated in code
- [ ] All ad slot IDs configured
- [ ] Privacy Policy and Terms of Service live
- [ ] Test mode disabled in production
- [ ] Mobile responsiveness tested
- [ ] Page speed optimized
- [ ] Analytics tracking setup
- [ ] GDPR compliance verified
- [ ] Content quality reviewed

## 📞 Support

For AdSense-specific issues:
- [Google AdSense Help Center](https://support.google.com/adsense/)
- [AdSense Community Forum](https://support.google.com/adsense/community)

For implementation issues:
- Check the component documentation in `/src/components/ads/`
- Review the test mode functionality
- Monitor browser console for errors

---

**Note**: Always test thoroughly in development before deploying to production. The test mode system allows you to perfect your ad placements without affecting real AdSense metrics.