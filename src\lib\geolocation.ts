/**
 * مكتبة تحديد الموقع الجغرافي المحسنة
 * Enhanced geolocation detection library
 */

import { getCountryCurrency } from './currency';

/**
 * خيارات تحديد الموقع
 * Geolocation options
 */
interface GeolocationOptions {
  readonly timeout: number;
  readonly enableHighAccuracy: boolean;
  readonly maximumAge: number;
}

/**
 * معلومات الموقع
 * Location info interface
 */
interface LocationInfo {
  readonly country: string;
  readonly countryCode: string;
  readonly currency: string;
  readonly city?: string;
  readonly region?: string;
  readonly timezone?: string;
}

/**
 * خطأ تحديد الموقع
 * Geolocation error
 */
export class GeolocationError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly originalError?: Error
  ) {
    super(message);
    this.name = 'GeolocationError';
  }
}

/**
 * الإعدادات الافتراضية لتحديد الموقع
 * Default geolocation settings
 */
const DEFAULT_OPTIONS: GeolocationOptions = {
  timeout: 10000, // 10 ثوانٍ
  enableHighAccuracy: false,
  maximumAge: 300000, // 5 دقائق
};

/**
 * خريطة الدول إلى العملات
 * Country to currency mapping
 */
const COUNTRY_CURRENCY_MAP: Record<string, string> = {
  'AE': 'AED',
  'SA': 'SAR',
  'EG': 'EGP',
  'KW': 'KWD',
  'QA': 'QAR',
  'BH': 'BHD',
  'OM': 'OMR',
  'JO': 'JOD',
  'MA': 'MAD',
  'TN': 'TND',
  'DZ': 'DZD',
  'LB': 'LBP',
  'IQ': 'IQD',
  'SY': 'SYP',
  'YE': 'YER',
  'LY': 'LYD',
  'SD': 'SDG',
  'US': 'USD',
  'GB': 'GBP',
  'FR': 'EUR',
  'DE': 'EUR',
  'IT': 'EUR',
  'ES': 'EUR',
  'NL': 'EUR',
  'BE': 'EUR',
  'AT': 'EUR',
  'PT': 'EUR',
  'IE': 'EUR',
  'FI': 'EUR',
  'GR': 'EUR',
  'LU': 'EUR',
  'MT': 'EUR',
  'CY': 'EUR',
  'SK': 'EUR',
  'SI': 'EUR',
  'EE': 'EUR',
  'LV': 'EUR',
  'LT': 'EUR',
};

/**
 * تحديد الموقع باستخدام IP
 * IP-based location detection
 */
async function detectLocationByIP(): Promise<LocationInfo> {
  try {
    // استخدام خدمة مجانية لتحديد الموقع
    const response = await fetch('https://ipapi.co/json/', {
      signal: AbortSignal.timeout(5000),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.error) {
      throw new Error(data.reason || 'IP geolocation failed');
    }

    const countryCode = data.country_code?.toUpperCase();
    const currency = COUNTRY_CURRENCY_MAP[countryCode] || 'USD';

    return {
      country: data.country_name || 'Unknown',
      countryCode: countryCode || 'US',
      currency,
      city: data.city,
      region: data.region,
      timezone: data.timezone,
    };
  } catch (error) {
    console.warn('IP geolocation failed:', error);
    throw new GeolocationError(
      'Failed to detect location via IP',
      'IP_DETECTION_FAILED',
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * تحديد الموقع باستخدام GPS
 * GPS-based location detection
 */
async function detectLocationByGPS(options: GeolocationOptions = DEFAULT_OPTIONS): Promise<LocationInfo> {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new GeolocationError(
        'Geolocation is not supported by this browser',
        'NOT_SUPPORTED'
      ));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;
          
          // تحويل الإحداثيات إلى معلومات البلد
          const locationInfo = await coordinatesToCountry(latitude, longitude);
          resolve(locationInfo);
        } catch (error) {
          reject(new GeolocationError(
            'Failed to convert coordinates to country',
            'COORDINATE_CONVERSION_FAILED',
            error instanceof Error ? error : new Error(String(error))
          ));
        }
      },
      (error) => {
        let code: string;
        let message: string;

        switch (error.code) {
          case error.PERMISSION_DENIED:
            code = 'PERMISSION_DENIED';
            message = 'Location access denied by user';
            break;
          case error.POSITION_UNAVAILABLE:
            code = 'POSITION_UNAVAILABLE';
            message = 'Location information unavailable';
            break;
          case error.TIMEOUT:
            code = 'TIMEOUT';
            message = 'Location request timed out';
            break;
          default:
            code = 'UNKNOWN_ERROR';
            message = 'Unknown geolocation error';
        }

        reject(new GeolocationError(message, code));
      },
      options
    );
  });
}

/**
 * تحويل الإحداثيات إلى بلد
 * Convert coordinates to country
 */
async function coordinatesToCountry(latitude: number, longitude: number): Promise<LocationInfo> {
  try {
    // استخدام خدمة مجانية للجيوكودينغ العكسي
    const response = await fetch(
      `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`,
      { signal: AbortSignal.timeout(5000) }
    );

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const countryCode = data.countryCode?.toUpperCase();
    const currency = COUNTRY_CURRENCY_MAP[countryCode] || 'USD';

    return {
      country: data.countryName || 'Unknown',
      countryCode: countryCode || 'US',
      currency,
      city: data.city,
      region: data.principalSubdivision,
    };
  } catch (error) {
    console.warn('Coordinate conversion failed:', error);
    throw new GeolocationError(
      'Failed to convert coordinates to country',
      'COORDINATE_CONVERSION_FAILED',
      error instanceof Error ? error : new Error(String(error))
    );
  }
}

/**
 * تحديد الموقع باستخدام المنطقة الزمنية
 * Timezone-based location detection
 */
function detectLocationByTimezone(): LocationInfo {
  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    
    // خريطة بسيطة للمناطق الزمنية إلى البلدان
    const timezoneCountryMap: Record<string, { country: string; countryCode: string }> = {
      'Asia/Dubai': { country: 'United Arab Emirates', countryCode: 'AE' },
      'Asia/Riyadh': { country: 'Saudi Arabia', countryCode: 'SA' },
      'Africa/Cairo': { country: 'Egypt', countryCode: 'EG' },
      'Asia/Kuwait': { country: 'Kuwait', countryCode: 'KW' },
      'Asia/Qatar': { country: 'Qatar', countryCode: 'QA' },
      'Asia/Bahrain': { country: 'Bahrain', countryCode: 'BH' },
      'Asia/Muscat': { country: 'Oman', countryCode: 'OM' },
      'Asia/Amman': { country: 'Jordan', countryCode: 'JO' },
      'Africa/Casablanca': { country: 'Morocco', countryCode: 'MA' },
      'Africa/Tunis': { country: 'Tunisia', countryCode: 'TN' },
      'Africa/Algiers': { country: 'Algeria', countryCode: 'DZ' },
      'Asia/Beirut': { country: 'Lebanon', countryCode: 'LB' },
      'Asia/Baghdad': { country: 'Iraq', countryCode: 'IQ' },
      'Asia/Damascus': { country: 'Syria', countryCode: 'SY' },
      'Asia/Aden': { country: 'Yemen', countryCode: 'YE' },
      'Africa/Tripoli': { country: 'Libya', countryCode: 'LY' },
      'Africa/Khartoum': { country: 'Sudan', countryCode: 'SD' },
    };

    const locationData = timezoneCountryMap[timezone];
    
    if (locationData) {
      const currency = COUNTRY_CURRENCY_MAP[locationData.countryCode] || 'USD';
      return {
        country: locationData.country,
        countryCode: locationData.countryCode,
        currency,
        timezone,
      };
    }

    // إذا لم نجد تطابق، استخدم الافتراضي
    return {
      country: 'Morocco',
      countryCode: 'MA',
      currency: 'MAD',
      timezone,
    };
  } catch (error) {
    console.warn('Timezone detection failed:', error);
    
    // قيمة افتراضية آمنة
    return {
      country: 'Morocco',
      countryCode: 'MA',
      currency: 'MAD',
      timezone: 'Africa/Casablanca',
    };
  }
}

/**
 * اكتشاف العملة بطريقة ذكية
 * Smart currency detection
 */
export async function smartCurrencyDetection(): Promise<string> {
  try {
    // أولوية الطرق: IP > Timezone > Default (تم إزالة GPS لتجنب طلب الإذن)
    const detectionMethods = [
      () => detectLocationByIP(),
      () => Promise.resolve(detectLocationByTimezone()),
      // تم إزالة طريقة GPS لتجنب طلب إذن الموقع من المستخدم
    ];

    let lastError: Error | null = null;

    for (const method of detectionMethods) {
      try {
        const locationInfo = await method();
        if (locationInfo.currency) {
          console.log(`Currency detected: ${locationInfo.currency} (${locationInfo.country})`);
          return locationInfo.currency;
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.warn('Detection method failed, trying next...', error);
        continue;
      }
    }

    // إذا فشلت جميع الطرق، استخدم الافتراضي
    console.warn('All detection methods failed, using default currency');
    return 'MAD'; // العملة الافتراضية
    
  } catch (error) {
    console.error('Smart currency detection failed:', error);
    return 'MAD'; // العملة الافتراضية
  }
}

/**
 * الحصول على معلومات الموقع الكاملة
 * Get complete location information
 */
export async function getLocationInfo(): Promise<LocationInfo | null> {
  try {
    const detectionMethods = [
      () => detectLocationByIP(),
      () => Promise.resolve(detectLocationByTimezone()),
      // تم إزالة طريقة GPS لتجنب طلب إذن الموقع من المستخدم
    ];

    for (const method of detectionMethods) {
      try {
        const locationInfo = await method();
        return locationInfo;
      } catch (error) {
        console.warn('Location detection method failed:', error);
        continue;
      }
    }

    return null;
  } catch (error) {
    console.error('Get location info failed:', error);
    return null;
  }
}

/**
 * التحقق من دعم تحديد الموقع
 * Check geolocation support
 */
export function isGeolocationSupported(): boolean {
  return typeof navigator !== 'undefined' && 'geolocation' in navigator;
}

/**
 * التحقق من صلاحيات تحديد الموقع
 * Check geolocation permissions
 */
export async function checkGeolocationPermission(): Promise<PermissionState | null> {
  try {
    if (typeof navigator === 'undefined' || !navigator.permissions) {
      return null;
    }

    const permission = await navigator.permissions.query({ name: 'geolocation' });
    return permission.state;
  } catch (error) {
    console.warn('Failed to check geolocation permission:', error);
    return null;
  }
}

/**
 * تصدير الأنواع
 * Export types
 */
export type { LocationInfo, GeolocationOptions };
