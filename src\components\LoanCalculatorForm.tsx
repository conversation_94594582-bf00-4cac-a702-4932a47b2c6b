"use client";

/**
 * مكون حاسبة القرض المحسن مع أفضل الممارسات البرمجية
 * Enhanced loan calculator component with best practices
 */

import { useState, useCallback, useMemo, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { 
  Landmark, 
  Percent, 
  CalendarClock, 
  Calculator, 
  BookOpen, 
  HelpCircle, 
  AlertTriangle, 
  CheckCircle 
} from "lucide-react";
import Link from "next/link";

// استيراد الأنواع والثوابت
import type { LoanFormData, LoanCalculationResult, DictionaryKeys, Locale, ChartDataPoint } from "@/types";
import { LOAN_CALCULATOR_SETTINGS, CHART_SETTINGS, STORAGE_KEYS } from "@/constants";

// استيراد المكونات
import { SchemaMarkup } from "@/components/seo/SchemaMarkup";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { ChartContainer, ChartConfig } from "@/components/ui/chart";
import { PieChart, Pie, Cell, Tooltip } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";
import CurrencySelector from "@/components/CurrencySelector";

// استيراد المساعدات
import { formatCurrency, getDefaultCurrency } from "@/lib/currency";
import { 
  calculateLoanSafely, 
  validateLoanInput, 
  debounce, 
  saveToLocalStorage, 
  getFromLocalStorage,
  createErrorInfo,
  cn 
} from "@/lib/utils";

/**
 * خصائص مكون حاسبة القرض
 * Loan calculator component props
 */
interface LoanCalculatorFormProps {
  readonly dict: DictionaryKeys;
  readonly lang: Locale;
}

/**
 * حالة الحساب
 * Calculation state
 */
interface CalculationState {
  readonly isCalculating: boolean;
  readonly result: LoanCalculationResult | null;
  readonly error: string | null;
  readonly hasCalculated: boolean;
}

/**
 * خصائص التلميح المخصص للرسم البياني
 * Custom tooltip props for chart
 */
interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    name: string;
    value: number;
    payload: { fill: string };
  }>;
  lang: Locale;
  currency: string;
}

/**
 * مكون التلميح المخصص للرسم البياني
 * Custom tooltip component for chart
 */
const CustomTooltip = ({ active, payload, lang, currency }: CustomTooltipProps) => {
  if (active && payload && payload.length > 0) {
    return (
      <div className="rounded-lg border bg-background p-2.5 shadow-sm">
        <div className="grid gap-1.5">
          {payload.map((item, index) => (
            <div key={`${item.name}-${index}`} className="flex items-center gap-2">
              <div 
                className="w-2.5 h-2.5 rounded-full" 
                style={{ backgroundColor: item.payload.fill }} 
              />
              <div className="flex justify-between flex-1 gap-2">
                <span className="text-muted-foreground">{item.name}:</span>
                <span className="font-bold">
                  {formatCurrency(item.value, currency, lang)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
  return null;
};

/**
 * مخطط التحقق من صحة البيانات
 * Data validation schema
 */
const createValidationSchema = (dict: DictionaryKeys) => z.object({
  loanAmount: z.string().optional(),
  interestRate: z.string().optional(),
  loanYears: z.string().optional(),
});

/**
 * نوع بيانات النموذج
 * Form data type
 */
type FormData = z.infer<ReturnType<typeof createValidationSchema>>;

/**
 * مكون حاسبة القرض الرئيسي
 * Main loan calculator component
 */
export default function LoanCalculatorForm({ dict, lang }: LoanCalculatorFormProps) {
  // الحالة المحلية
  const [calculationState, setCalculationState] = useState<CalculationState>({
    isCalculating: false,
    result: null,
    error: null,
    hasCalculated: false,
  });
  
  const [selectedCurrency, setSelectedCurrency] = useState<string>(() => 
    getFromLocalStorage(STORAGE_KEYS.SELECTED_CURRENCY, getDefaultCurrency())
  );

  // إعداد النموذج مع التحقق من صحة البيانات
  const formSchema = useMemo(() => createValidationSchema(dict), [dict]);
  
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {},
    mode: 'onChange', // التحقق المستمر من صحة البيانات
  });

  // حفظ العملة المختارة في التخزين المحلي
  useEffect(() => {
    saveToLocalStorage(STORAGE_KEYS.SELECTED_CURRENCY, selectedCurrency);
  }, [selectedCurrency]);

  /**
   * معالج إرسال النموذج مع الحساب الآمن
   * Form submission handler with safe calculation
   */
  const handleFormSubmit = useCallback((data: FormData) => {
    // تحويل القيم النصية إلى أرقام
    const loanAmount = data.loanAmount ? parseFloat(data.loanAmount) : undefined;
    const interestRate = data.interestRate ? parseFloat(data.interestRate) : undefined;
    const loanYears = data.loanYears ? parseInt(data.loanYears) : undefined;

    // التحقق من وجود جميع القيم المطلوبة
    if (!loanAmount || !interestRate || !loanYears) {
      setCalculationState(prev => ({
        ...prev,
        error: 'Please fill in all required fields',
        isCalculating: false,
      }));
      return;
    }

    // التحقق من صحة البيانات
    const validationErrors = validateLoanInput({
      loanAmount,
      interestRate,
      loanYears,
    });
    if (validationErrors.length > 0) {
      setCalculationState(prev => ({
        ...prev,
        error: validationErrors[0]?.message || 'Invalid input',
        isCalculating: false,
      }));
      return;
    }

    setCalculationState(prev => ({ ...prev, isCalculating: true, error: null }));

    // محاكاة تأخير الحساب للتجربة المستخدم
    setTimeout(() => {
      const result = calculateLoanSafely(
        loanAmount,
        interestRate,
        loanYears
      );

      if (result) {
        setCalculationState({
          isCalculating: false,
          result,
          error: null,
          hasCalculated: true,
        });

        // حفظ في تاريخ الحسابات
        const calculationHistory = getFromLocalStorage(STORAGE_KEYS.CALCULATION_HISTORY, []);
        const newCalculation = {
          ...data,
          ...result,
          currency: selectedCurrency,
          timestamp: new Date().toISOString(),
        };
        
        saveToLocalStorage(STORAGE_KEYS.CALCULATION_HISTORY, [
          newCalculation,
          ...calculationHistory.slice(0, 9) // الاحتفاظ بآخر 10 حسابات
        ]);
      } else {
        setCalculationState({
          isCalculating: false,
          result: null,
          error: 'حدث خطأ في الحساب',
          hasCalculated: false,
        });
      }
    }, LOAN_CALCULATOR_SETTINGS.CALCULATION_DELAY_MS);
  }, [selectedCurrency]);

  // بيانات الرسم البياني
  const chartData: ChartDataPoint[] = useMemo(() => {
    if (!calculationState.result) return [];
    
    return [
      { 
        name: dict.results.pieChartPrincipal, 
        value: calculationState.result.principal, 
        fill: CHART_SETTINGS.COLORS.PRIMARY 
      },
      { 
        name: dict.results.pieChartTotalInterest, 
        value: calculationState.result.totalInterest, 
        fill: CHART_SETTINGS.COLORS.DANGER 
      },
    ];
  }, [calculationState.result, dict.results]);

  // إعداد الرسم البياني
  const chartConfig: ChartConfig = useMemo(() => ({
    principal: {
      label: dict.results.pieChartPrincipal,
      color: CHART_SETTINGS.COLORS.PRIMARY,
    },
    interest: {
      label: dict.results.pieChartTotalInterest,
      color: CHART_SETTINGS.COLORS.DANGER,
    },
  }), [dict.results]);

  /**
   * مكون تحذير التكلفة الحقيقية للربا
   * Real cost warning component
   */
  const RibaWarning = useCallback(() => {
    if (!calculationState.result || calculationState.result.totalInterest <= 0 || !dict.warnings) {
      return null;
    }

    const { result } = calculationState;
    const interestPercentage = ((result.totalInterest / result.principal) * 100).toFixed(1);

    return (
      <div className="mt-6 space-y-4">
        {/* تحذير التكلفة الحقيقية */}
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-6 h-6 text-red-600 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h4 className="text-red-800 font-bold text-sm mb-2">
                {dict.warnings.ribaWarningTitle || 'Warning: Real Cost of Interest'}
              </h4>
              <div className="space-y-2 text-xs text-red-700">
                <p>
                  {(dict.warnings.ribaWarningText || 'You will pay {amount} extra in interest!').replace('{amount}', formatCurrency(result.totalInterest, selectedCurrency, lang))}
                </p>
                <p className="font-semibold">
                  {(dict.warnings.ribaWarningPercentage || 'Increase: {percentage}% of original amount').replace('{percentage}', interestPercentage)}
                </p>
                {lang === 'ar' && dict.warnings.ribaWarningVerse && (
                  <p className="text-red-800 font-bold">
                    {dict.warnings.ribaWarningVerse}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* البدائل الإسلامية */}
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-start gap-3">
            <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h4 className="text-green-800 font-bold text-sm mb-2">
                {dict.warnings.islamicAlternativesTitle || 'Safe Islamic Alternatives'}
              </h4>
              <div className="space-y-1 text-xs text-green-700">
                <p>• {dict.warnings.murabaha || 'Murabaha: Bank purchases and resells with known profit'}</p>
                <p>• {dict.warnings.ijara || 'Ijara: Lease with promise of ownership'}</p>
                <p>• {dict.warnings.musharaka || 'Musharaka: Fair investment partnership'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }, [calculationState.result, selectedCurrency, lang, dict.warnings]);

  return (
    <>
      <SchemaMarkup type="calculator" lang={lang} />
      <div className="flex min-h-screen w-full flex-col items-center justify-center bg-background p-4 sm:p-8 pt-20">
        <div className="w-full max-w-4xl mx-auto container-stable">
          {/* العنوان الرئيسي */}
          <div className={cn("text-center mb-8", lang === 'ar' && "space-y-4")}>
            <h1 className={cn(
              "text-4xl sm:text-5xl font-bold text-primary tracking-tight",
              lang === 'ar' && "font-arabic-display leading-tight"
            )}>
              {dict.header.title}
            </h1>
            <p className={cn(
              "text-muted-foreground mt-2 text-lg",
              lang === 'ar' && "text-xl leading-relaxed font-arabic"
            )}>
              {dict.header.subtitle}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 grid-stable auto-rows-auto">
            {/* نموذج الحاسبة */}
            <Card className="card-stable">
              <CardHeader className={lang === 'ar' ? 'pb-6' : ''}>
                <CardTitle className={cn(
                  "text-2xl",
                  lang === 'ar' && "font-arabic-display text-3xl leading-tight"
                )}>
                  {dict.calculator.title}
                </CardTitle>
                <CardDescription className={cn(
                  lang === 'ar' && "text-lg leading-relaxed font-arabic mt-3"
                )}>
                  {dict.calculator.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
                    {/* محدد العملة */}
                    <CurrencySelector
                      selectedCurrency={selectedCurrency}
                      onCurrencyChange={setSelectedCurrency}
                      lang={lang}
                      dict={dict}
                      autoDetect={true}
                    />

                    {/* مبلغ القرض */}
                    <FormField
                      control={form.control}
                      name="loanAmount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="form-label">
                            {dict.calculator.loanAmount}
                          </FormLabel>
                          <div className="relative input-wrapper">
                            <Landmark className={cn(
                              "absolute top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground input-icon",
                              lang === 'ar' ? "right-3" : "left-3"
                            )} />
                            <FormControl>
                              <Input
                                type="number"
                                step="100"
                                placeholder={dict.calculator.loanAmountPlaceholder}
                                className={cn(
                                  lang === 'ar' ? "pr-10 pl-3 text-right" : "pl-10 pr-3"
                                )}
                                dir={lang === 'ar' ? "rtl" : "ltr"}
                                {...field}
                                onChange={(e) => field.onChange(e.target.value)}
                                value={field.value || ""}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* معدل الفائدة */}
                    <FormField
                      control={form.control}
                      name="interestRate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="form-label">
                            {dict.calculator.interestRate}
                          </FormLabel>
                          <div className="relative input-wrapper">
                            <Percent className={cn(
                              "absolute top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground input-icon",
                              lang === 'ar' ? "right-3" : "left-3"
                            )} />
                            <FormControl>
                              <Input
                                type="number"
                                step="0.1"
                                placeholder={dict.calculator.interestRatePlaceholder}
                                className={cn(
                                  lang === 'ar' ? "pr-10 pl-3 text-right" : "pl-10 pr-3"
                                )}
                                dir={lang === 'ar' ? "rtl" : "ltr"}
                                {...field}
                                onChange={(e) => field.onChange(e.target.value)}
                                value={field.value || ""}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* مدة القرض */}
                    <FormField
                      control={form.control}
                      name="loanYears"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="form-label">
                            {dict.calculator.loanTerm}
                          </FormLabel>
                          <div className="relative input-wrapper">
                            <CalendarClock className={cn(
                              "absolute top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground input-icon",
                              lang === 'ar' ? "right-3" : "left-3"
                            )} />
                            <FormControl>
                              <Input
                                type="number"
                                placeholder={dict.calculator.loanTermPlaceholder}
                                className={cn(
                                  lang === 'ar' ? "pr-10 pl-3 text-right" : "pl-10 pr-3"
                                )}
                                dir={lang === 'ar' ? "rtl" : "ltr"}
                                {...field}
                                onChange={(e) => field.onChange(e.target.value)}
                                value={field.value || ""}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* زر الحساب */}
                    <Button 
                      type="submit" 
                      className={cn(
                        "w-full !mt-8",
                        lang === 'ar' && "h-12 text-lg font-arabic font-semibold"
                      )} 
                      disabled={calculationState.isCalculating}
                    >
                      <Calculator className={cn(
                        "h-5 w-5",
                        lang === 'ar' ? "ml-3" : "mr-2"
                      )} />
                      <span className={lang === 'ar' ? 'font-arabic' : ''}>
                        {calculationState.isCalculating 
                          ? dict.calculator.buttonLoading 
                          : dict.calculator.button
                        }
                      </span>
                    </Button>

                    {/* رسالة الخطأ */}
                    {calculationState.error && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                        {calculationState.error}
                      </div>
                    )}
                  </form>
                </Form>
              </CardContent>
            </Card>

            {/* النتائج */}
            <Card className="h-full flex flex-col results-stable overflow-hidden">
              <CardHeader className={lang === 'ar' ? 'pb-6' : ''}>
                <CardTitle className={cn(
                  "text-2xl",
                  lang === 'ar' && "font-arabic-display text-3xl leading-tight"
                )}>
                  {dict.results.title}
                </CardTitle>
                <CardDescription className={cn(
                  lang === 'ar' && "text-lg leading-relaxed font-arabic mt-3"
                )}>
                  {dict.results.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                {calculationState.isCalculating ? (
                  <div className="flex flex-col items-center justify-center space-y-4 w-full p-8">
                    <Skeleton className="h-48 w-48 rounded-full" />
                    <div className="space-y-2 w-full px-4">
                      <Skeleton className="h-6 w-3/4 mx-auto" />
                      <Skeleton className="h-6 w-1/2 mx-auto" />
                    </div>
                  </div>
                ) : calculationState.result ? (
                  <>
                    <div className="w-full flex justify-center my-4">
                      <div className="w-64 h-64">
                        <PieChart width={250} height={250}>
                          <Tooltip
                            cursor={false}
                            content={<CustomTooltip lang={lang} currency={selectedCurrency} />}
                          />
                          <Pie
                            data={chartData}
                            dataKey="value"
                            nameKey="name"
                            innerRadius={CHART_SETTINGS.INNER_RADIUS}
                            outerRadius={CHART_SETTINGS.OUTER_RADIUS}
                            paddingAngle={CHART_SETTINGS.PADDING_ANGLE}
                            cx="50%"
                            cy="50%"
                          >
                            {chartData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.fill} />
                            ))}
                          </Pie>
                        </PieChart>
                      </div>
                    </div>

                    {/* نتائج الحسابات */}
                    <div className="w-full mt-2 space-y-3">
                      {/* المبلغ الأصلي */}
                      <div className={cn(
                        "flex justify-between items-center w-full",
                        lang === 'ar' && "gap-6 py-3"
                      )}>
                        {lang === 'ar' ? (
                          <>
                            <span className="font-semibold text-lg text-left flex-shrink-0 bg-muted/30 px-3 py-1 rounded-md" dir="ltr">
                              {formatCurrency(calculationState.result.principal, selectedCurrency, lang)}
                            </span>
                            <span className="text-base text-foreground flex-shrink-0 max-w-[55%] font-medium">
                              {dict.results.principal}
                            </span>
                          </>
                        ) : (
                          <>
                            <span className="text-sm text-muted-foreground flex-shrink-0 max-w-[60%]">
                              {dict.results.principal}
                            </span>
                            <span className="font-semibold text-base text-right flex-shrink-0" dir="ltr">
                              {formatCurrency(calculationState.result.principal, selectedCurrency, lang)}
                            </span>
                          </>
                        )}
                      </div>

                      {/* إجمالي الفوائد */}
                      <div className={cn(
                        "flex justify-between items-center w-full",
                        lang === 'ar' && "gap-6 py-3"
                      )}>
                        {lang === 'ar' ? (
                          <>
                            <span className="font-semibold text-lg text-red-600 text-left flex-shrink-0 bg-red-50 px-3 py-1 rounded-md border border-red-200" dir="ltr">
                              {formatCurrency(calculationState.result.totalInterest, selectedCurrency, lang)}
                            </span>
                            <span className="text-base text-red-700 flex-shrink-0 max-w-[55%] font-medium">
                              {dict.results.totalInterest}
                            </span>
                          </>
                        ) : (
                          <>
                            <span className="text-sm text-red-600 flex-shrink-0 max-w-[60%]">
                              {dict.results.totalInterest}
                            </span>
                            <span className="font-semibold text-base text-red-600 text-right flex-shrink-0" dir="ltr">
                              {formatCurrency(calculationState.result.totalInterest, selectedCurrency, lang)}
                            </span>
                          </>
                        )}
                      </div>

                      <Separator className="my-2" />

                      {/* إجمالي السداد */}
                      <div className={cn(
                        "flex justify-between items-center font-bold w-full",
                        lang === 'ar' && "gap-6 py-4 bg-primary/5 rounded-lg px-4"
                      )}>
                        {lang === 'ar' ? (
                          <>
                            <span className="text-xl text-left flex-shrink-0 text-primary font-bold" dir="ltr">
                              {formatCurrency(calculationState.result.totalPayment, selectedCurrency, lang)}
                            </span>
                            <span className="text-primary flex-shrink-0 max-w-[55%] font-bold text-lg">
                              {dict.results.totalRepayment}
                            </span>
                          </>
                        ) : (
                          <>
                            <span className="text-primary flex-shrink-0 max-w-[60%]">
                              {dict.results.totalRepayment}
                            </span>
                            <span className="text-lg text-right flex-shrink-0" dir="ltr">
                              {formatCurrency(calculationState.result.totalPayment, selectedCurrency, lang)}
                            </span>
                          </>
                        )}
                      </div>

                      {/* القسط الشهري */}
                      <div className={cn(
                        "flex justify-between items-center w-full pt-3 border-t border-dashed",
                        lang === 'ar' && "gap-6 py-3"
                      )}>
                        {lang === 'ar' ? (
                          <>
                            <span className="font-semibold text-lg text-left flex-shrink-0 bg-primary/10 px-3 py-1 rounded-md" dir="ltr">
                              {formatCurrency(calculationState.result.monthlyPayment, selectedCurrency, lang)}
                            </span>
                            <span className="text-base text-foreground flex-shrink-0 max-w-[55%] font-medium">
                              {dict.results.monthlyPayment}
                            </span>
                          </>
                        ) : (
                          <>
                            <span className="text-sm text-muted-foreground flex-shrink-0 max-w-[60%]">
                              {dict.results.monthlyPayment}
                            </span>
                            <span className="font-semibold text-base text-right flex-shrink-0" dir="ltr">
                              {formatCurrency(calculationState.result.monthlyPayment, selectedCurrency, lang)}
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </>
                ) : (
                  <div className={cn(
                    "text-center text-muted-foreground p-8",
                    lang === 'ar' && "font-arabic"
                  )}>
                    <Calculator className="mx-auto h-16 w-16 mb-4 opacity-50" />
                    <p className={cn(
                      "text-lg font-medium",
                      lang === 'ar' && "text-xl leading-relaxed"
                    )}>
                      {dict.results.placeholderTitle}
                    </p>
                    <p className={cn(
                      "text-sm",
                      lang === 'ar' && "text-base leading-relaxed mt-3"
                    )}>
                      {dict.results.placeholderDescription}
                    </p>
                  </div>
                )}
              </CardContent>
              
              {/* تم إزالة CardFooter لأن النتائج مضمنة الآن في CardContent */}
           </Card>
         </div>

         {/* تحذير الربا */}
         <RibaWarning />

         {/* قسم المقالات والمعلومات التعليمية */}
         {calculationState.hasCalculated && (
           <div className="mt-12 space-y-8">
             {/* مقال عن مخاطر الربا */}
             <Card className="card-stable">
               <CardHeader>
                 <CardTitle className={cn(
                   "text-2xl flex items-center gap-3",
                   lang === 'ar' && "font-arabic-display text-3xl leading-tight"
                 )}>
                   <BookOpen className="h-6 w-6 text-primary" />
                   {dict.education?.ribaRisksTitle || 'Understanding Interest Risks'}
                 </CardTitle>
               </CardHeader>
               <CardContent className="space-y-4">
                 <div className={cn(
                   "prose prose-slate max-w-none",
                   lang === 'ar' && "font-arabic text-lg leading-relaxed"
                 )}>
                   {dict.education && (
                     <>
                       <p>{dict.education.ribaRisksContent1}</p>
                       <p>{dict.education.ribaRisksContent2}</p>
                       <p>{dict.education.ribaRisksContent3}</p>
                     </>
                   )}
                 </div>
               </CardContent>
             </Card>

             {/* قسم الأسئلة الشائعة */}
             <Card className="card-stable">
               <CardHeader>
                 <CardTitle className={cn(
                   "text-2xl flex items-center gap-3",
                   lang === 'ar' && "font-arabic-display text-3xl leading-tight"
                 )}>
                   <HelpCircle className="h-6 w-6 text-primary" />
                   {dict.faq?.title || 'Frequently Asked Questions'}
                 </CardTitle>
               </CardHeader>
               <CardContent className="space-y-6">
                 <div className="space-y-4">
                   {/* سؤال 1 */}
                   <div className="border-l-4 border-primary pl-4">
                     <h4 className={cn(
                       "font-semibold text-foreground mb-2",
                       lang === 'ar' && "font-arabic-display text-lg leading-tight"
                     )}>
                       {dict.education?.faqQuestion1 || 'What are Islamic alternatives to traditional loans?'}
                     </h4>
                     <p className={cn(
                       "text-muted-foreground text-sm",
                       lang === 'ar' && "font-arabic text-base leading-relaxed"
                     )}>
                       {dict.education?.faqAnswer1 || 'Murabaha, Ijara, Musharaka, and Mudaraba - all are Sharia-compliant solutions that achieve fairness.'}
                     </p>
                   </div>

                   {/* سؤال 2 */}
                   <div className="border-l-4 border-primary pl-4">
                     <h4 className={cn(
                       "font-semibold text-foreground mb-2",
                       lang === 'ar' && "font-arabic-display text-lg leading-tight"
                     )}>
                       {dict.education?.faqQuestion2 || 'How can I avoid falling into the debt trap?'}
                     </h4>
                     <p className={cn(
                       "text-muted-foreground text-sm",
                       lang === 'ar' && "font-arabic text-base leading-relaxed"
                     )}>
                       {dict.education?.faqAnswer2 || 'Set a clear budget, save before buying, and use Islamic financing alternatives.'}
                     </p>
                   </div>

                   {/* سؤال 3 */}
                   <div className="border-l-4 border-primary pl-4">
                     <h4 className={cn(
                       "font-semibold text-foreground mb-2",
                       lang === 'ar' && "font-arabic-display text-lg leading-tight"
                     )}>
                       {dict.education?.faqQuestion3 || 'Can I use this calculator to compare options?'}
                     </h4>
                     <p className={cn(
                       "text-muted-foreground text-sm",
                       lang === 'ar' && "font-arabic text-base leading-relaxed"
                     )}>
                       {dict.education?.faqAnswer3 || 'Yes, you can compare different interest rates to understand the real cost of traditional loans.'}
                     </p>
                   </div>
                 </div>
               </CardContent>
             </Card>
           </div>
         )}

       </div>
     </div>
   </>
 );
}
