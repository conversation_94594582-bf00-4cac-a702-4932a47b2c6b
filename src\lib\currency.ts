/**
 * مكتبة إدارة العملات المحسنة
 * Enhanced currency management library
 */

import type { Currency, Locale } from '@/types';
import { ARAB_CURRENCY_CODES, CURRENCY_SETTINGS } from '@/constants';

/**
 * قائمة العملات المدعومة
 * Supported currencies list
 */
export const CURRENCIES: Currency[] = [
  {
    code: 'USD',
    name: { en: 'US Dollar', fr: 'Dollar américain', ar: 'الدولار الأمريكي' },
    symbol: '$',
    country: { en: 'United States', fr: 'États-Unis', ar: 'الولايات المتحدة' },
    countryCode: 'US',
    locale: { en: 'en-US', fr: 'en-US', ar: 'en-US' }
  },
  {
    code: 'EUR',
    name: { en: 'Euro', fr: 'Euro', ar: 'اليورو' },
    symbol: '€',
    country: { en: 'European Union', fr: 'Union européenne', ar: 'الاتحاد الأوروبي' },
    countryCode: 'EU',
    locale: { en: 'en-EU', fr: 'fr-FR', ar: 'en-EU' }
  },
  {
    code: 'AED',
    name: { en: 'UAE Dirham', fr: 'Dirham des EAU', ar: 'الدرهم الإماراتي' },
    symbol: 'د.إ',
    country: { en: 'United Arab Emirates', fr: 'Émirats arabes unis', ar: 'الإمارات العربية المتحدة' },
    countryCode: 'AE',
    locale: { en: 'en-AE', fr: 'fr-AE', ar: 'ar-AE' }
  },
  {
    code: 'SAR',
    name: { en: 'Saudi Riyal', fr: 'Riyal saoudien', ar: 'الريال السعودي' },
    symbol: 'ر.س',
    country: { en: 'Saudi Arabia', fr: 'Arabie saoudite', ar: 'المملكة العربية السعودية' },
    countryCode: 'SA',
    locale: { en: 'en-SA', fr: 'fr-SA', ar: 'ar-SA' }
  },
  {
    code: 'EGP',
    name: { en: 'Egyptian Pound', fr: 'Livre égyptienne', ar: 'الجنيه المصري' },
    symbol: 'ج.م',
    country: { en: 'Egypt', fr: 'Égypte', ar: 'مصر' },
    countryCode: 'EG',
    locale: { en: 'en-EG', fr: 'fr-EG', ar: 'ar-EG' }
  },
  {
    code: 'KWD',
    name: { en: 'Kuwaiti Dinar', fr: 'Dinar koweïtien', ar: 'الدينار الكويتي' },
    symbol: 'د.ك',
    country: { en: 'Kuwait', fr: 'Koweït', ar: 'الكويت' },
    countryCode: 'KW',
    locale: { en: 'en-KW', fr: 'fr-KW', ar: 'ar-KW' }
  },
  {
    code: 'QAR',
    name: { en: 'Qatari Riyal', fr: 'Riyal qatarien', ar: 'الريال القطري' },
    symbol: 'ر.ق',
    country: { en: 'Qatar', fr: 'Qatar', ar: 'قطر' },
    countryCode: 'QA',
    locale: { en: 'en-QA', fr: 'fr-QA', ar: 'ar-QA' }
  },
  {
    code: 'BHD',
    name: { en: 'Bahraini Dinar', fr: 'Dinar bahreïni', ar: 'الدينار البحريني' },
    symbol: 'د.ب',
    country: { en: 'Bahrain', fr: 'Bahreïn', ar: 'البحرين' },
    countryCode: 'BH',
    locale: { en: 'en-BH', fr: 'fr-BH', ar: 'ar-BH' }
  },
  {
    code: 'OMR',
    name: { en: 'Omani Rial', fr: 'Rial omanais', ar: 'الريال العماني' },
    symbol: 'ر.ع',
    country: { en: 'Oman', fr: 'Oman', ar: 'عمان' },
    countryCode: 'OM',
    locale: { en: 'en-OM', fr: 'fr-OM', ar: 'ar-OM' }
  },
  {
    code: 'JOD',
    name: { en: 'Jordanian Dinar', fr: 'Dinar jordanien', ar: 'الدينار الأردني' },
    symbol: 'د.أ',
    country: { en: 'Jordan', fr: 'Jordanie', ar: 'الأردن' },
    countryCode: 'JO',
    locale: { en: 'en-JO', fr: 'fr-JO', ar: 'ar-JO' }
  },
  {
    code: 'MAD',
    name: { en: 'Moroccan Dirham', fr: 'Dirham marocain', ar: 'الدرهم المغربي' },
    symbol: 'د.م',
    country: { en: 'Morocco', fr: 'Maroc', ar: 'المغرب' },
    countryCode: 'MA',
    locale: { en: 'en-MA', fr: 'fr-MA', ar: 'ar-MA' }
  },
  {
    code: 'TND',
    name: { en: 'Tunisian Dinar', fr: 'Dinar tunisien', ar: 'الدينار التونسي' },
    symbol: 'د.ت',
    country: { en: 'Tunisia', fr: 'Tunisie', ar: 'تونس' },
    countryCode: 'TN',
    locale: { en: 'en-TN', fr: 'fr-TN', ar: 'ar-TN' }
  },
  {
    code: 'DZD',
    name: { en: 'Algerian Dinar', fr: 'Dinar algérien', ar: 'الدينار الجزائري' },
    symbol: 'د.ج',
    country: { en: 'Algeria', fr: 'Algérie', ar: 'الجزائر' },
    countryCode: 'DZ',
    locale: { en: 'en-DZ', fr: 'fr-DZ', ar: 'ar-DZ' }
  },
  {
    code: 'LBP',
    name: { en: 'Lebanese Pound', fr: 'Livre libanaise', ar: 'الليرة اللبنانية' },
    symbol: 'ل.ل',
    country: { en: 'Lebanon', fr: 'Liban', ar: 'لبنان' },
    countryCode: 'LB',
    locale: { en: 'en-LB', fr: 'fr-LB', ar: 'ar-LB' }
  },
  {
    code: 'IQD',
    name: { en: 'Iraqi Dinar', fr: 'Dinar irakien', ar: 'الدينار العراقي' },
    symbol: 'د.ع',
    country: { en: 'Iraq', fr: 'Irak', ar: 'العراق' },
    countryCode: 'IQ',
    locale: { en: 'en-IQ', fr: 'fr-IQ', ar: 'ar-IQ' }
  },
  {
    code: 'SYP',
    name: { en: 'Syrian Pound', fr: 'Livre syrienne', ar: 'الليرة السورية' },
    symbol: 'ل.س',
    country: { en: 'Syria', fr: 'Syrie', ar: 'سوريا' },
    countryCode: 'SY',
    locale: { en: 'en-SY', fr: 'fr-SY', ar: 'ar-SY' }
  },
  {
    code: 'YER',
    name: { en: 'Yemeni Rial', fr: 'Rial yéménite', ar: 'الريال اليمني' },
    symbol: 'ر.ي',
    country: { en: 'Yemen', fr: 'Yémen', ar: 'اليمن' },
    countryCode: 'YE',
    locale: { en: 'en-YE', fr: 'fr-YE', ar: 'ar-YE' }
  },
  {
    code: 'LYD',
    name: { en: 'Libyan Dinar', fr: 'Dinar libyen', ar: 'الدينار الليبي' },
    symbol: 'د.ل',
    country: { en: 'Libya', fr: 'Libye', ar: 'ليبيا' },
    countryCode: 'LY',
    locale: { en: 'en-LY', fr: 'fr-LY', ar: 'ar-LY' }
  },
  {
    code: 'SDG',
    name: { en: 'Sudanese Pound', fr: 'Livre soudanaise', ar: 'الجنيه السوداني' },
    symbol: 'ج.س',
    country: { en: 'Sudan', fr: 'Soudan', ar: 'السودان' },
    countryCode: 'SD',
    locale: { en: 'en-SD', fr: 'fr-SD', ar: 'ar-SD' }
  }
];

/**
 * الحصول على قائمة العملات
 * Get currencies list
 */
export function getCurrencyList(): Currency[] {
  return CURRENCIES;
}

/**
 * الحصول على بيانات عملة محددة
 * Get specific currency data
 */
export function getCurrency(code: string): Currency | undefined {
  return CURRENCIES.find(currency => currency.code === code.toUpperCase());
}

/**
 * التحقق من كون العملة عربية
 * Check if currency is Arab
 */
export function isArabCurrency(code: string): boolean {
  return ARAB_CURRENCY_CODES.includes(code.toUpperCase() as any);
}

/**
 * الحصول على العملة الافتراضية
 * Get default currency
 */
export function getDefaultCurrency(): string {
  return CURRENCY_SETTINGS.DEFAULT_CURRENCY;
}

/**
 * تنسيق العملة بطريقة آمنة ومحسنة
 * Format currency safely and efficiently
 */
export function formatCurrency(
  amount: number,
  currencyCode: string,
  locale: Locale = 'en',
  options: Intl.NumberFormatOptions = {}
): string {
  try {
    if (typeof amount !== 'number' || !isFinite(amount)) {
      return '0.00';
    }

    const currency = getCurrency(currencyCode);
    if (!currency) {
      // في حالة عدم العثور على العملة، يتم عرض المبلغ مع الكود
      return `${amount.toFixed(CURRENCY_SETTINGS.DECIMAL_PLACES)} ${currencyCode}`;
    }

    const localeString = currency.locale[locale] || `${locale}-u-nu-latn`;

    const formatOptions: Intl.NumberFormatOptions = {
      style: 'currency',
      currency: currency.code,
      minimumFractionDigits: CURRENCY_SETTINGS.DECIMAL_PLACES,
      maximumFractionDigits: CURRENCY_SETTINGS.DECIMAL_PLACES,
      ...options,
    };

    return new Intl.NumberFormat(localeString, formatOptions).format(amount);

  } catch (error) {
    console.error('Currency formatting error:', error);
    // رسالة خطأ احتياطية أكثر وضوحًا
    const symbol = getCurrencySymbol(currencyCode);
    return `${symbol} ${amount.toFixed(CURRENCY_SETTINGS.DECIMAL_PLACES)}`;
  }
}

/**
 * الحصول على رمز العملة
 * Get currency symbol
 */
export function getCurrencySymbol(code: string): string {
  const currency = getCurrency(code);
  return currency?.symbol || code;
}

/**
 * تحويل كود البلد إلى كود العملة
 * Convert country code to currency code
 */
export function getCountryCurrency(countryCode: string): string {
  const currency = CURRENCIES.find(c => c.countryCode === countryCode.toUpperCase());
  return currency?.code || getDefaultCurrency();
}

/**
 * الحصول على اسم العملة
 * Get currency name
 */
export function getCurrencyName(code: string, locale: Locale = 'en'): string {
  const currency = getCurrency(code);
  return currency?.name[locale] || code;
}

/**
 * الحصول على اسم البلد
 * Get country name
 */
export function getCountryName(currencyCode: string, locale: Locale = 'en'): string {
  const currency = getCurrency(currencyCode);
  return currency?.country[locale] || currencyCode;
}

/**
 * فلترة العملات حسب المنطقة
 * Filter currencies by region
 */
export function getCurrenciesByRegion(region: 'gcc' | 'maghreb' | 'mashreq' | 'other'): Currency[] {
  const regionMappings = {
    gcc: ['AED', 'SAR', 'KWD', 'QAR', 'BHD', 'OMR'],
    maghreb: ['MAD', 'TND', 'DZD', 'LYD'],
    mashreq: ['EGP', 'JOD', 'LBP', 'SYP'],
    other: ['IQD', 'YER', 'SDG']
  };

  const codes = regionMappings[region] || [];
  return CURRENCIES.filter(currency => codes.includes(currency.code));
}

/**
 * البحث في العملات
 * Search currencies
 */
export function searchCurrencies(query: string, locale: Locale = 'en'): Currency[] {
  if (!query || query.trim().length === 0) {
    return CURRENCIES;
  }

  const searchTerm = query.toLowerCase().trim();
  
  return CURRENCIES.filter(currency => {
    const name = currency.name[locale].toLowerCase();
    const country = currency.country[locale].toLowerCase();
    const code = currency.code.toLowerCase();
    
    return name.includes(searchTerm) || 
           country.includes(searchTerm) || 
           code.includes(searchTerm);
  });
}

/**
 * تصدير الأنواع
 * Export types
 */
export type { Currency };
