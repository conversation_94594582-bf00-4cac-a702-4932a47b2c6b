"use client";

import Link from "next/link";
import { Calculator, BookOpen, HelpCircle, Heart } from "lucide-react";

interface FooterArabicProps {
  dict: any;
}

export function FooterArabic({ dict }: FooterArabicProps) {
  const currentYear = new Date().getFullYear();

  const navigation = [
    {
      name: dict.navigation?.home || 'الحاسبة',
      href: '/ar',
      icon: Calculator,
    },
    {
      name: dict.navigation?.blog || 'المدونة',
      href: '/ar/blog',
      icon: BookOpen,
    },
    {
      name: dict.navigation?.faq || 'الأسئلة الشائعة',
      href: '/ar/faq',
      icon: HelpCircle,
    }
  ];

  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Brand */}
          <div className="space-y-6 text-right">
            <Link href="/ar" className="flex items-center gap-3 justify-end">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <Calculator className="h-6 w-6" />
              </div>
              <span className="text-2xl font-bold text-primary font-arabic-display">حساب الفائدة (الربا)</span>
            </Link>
            <p className="text-base text-muted-foreground max-w-sm text-right font-arabic leading-relaxed">
              {dict.footer?.description || 'حاسبة قروض مجانية وشاملة تدعم جميع العملات العربية. احسب الفوائد الثابتة والأقساط الشهرية والسداد الإجمالي بدقة عالية.'}
            </p>
          </div>

          {/* Navigation */}
          <div className="space-y-4 text-right">
            <h3 className="text-sm font-semibold text-foreground text-right">
              {dict.footer?.quickLinks || 'روابط سريعة'}
            </h3>
            <ul className="space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className="flex items-center justify-end gap-2 text-right text-sm text-muted-foreground hover:text-primary transition-colors"
                    >
                      <Icon className="h-4 w-4" />
                      <span>{item.name}</span>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>

          {/* Legal */}
          <div className="space-y-4 text-right">
            <h3 className="text-sm font-semibold text-foreground text-right">
              {dict.footer?.legal || 'قانوني'}
            </h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/ar/privacy-policy"
                  className="text-sm text-muted-foreground hover:text-foreground transition-colors text-right block"
                >
                  {dict.footer?.privacyPolicy || 'سياسة الخصوصية'}
                </Link>
              </li>
              <li>
                <Link
                  href="/ar/terms-of-service"
                  className="text-sm text-muted-foreground hover:text-foreground transition-colors text-right block"
                >
                  {dict.footer?.termsOfService || 'شروط الخدمة'}
                </Link>
              </li>
              <li>
                <span className="text-sm text-muted-foreground text-right block">
                  {dict.footer?.educationalPurpose || 'لأغراض تعليمية فقط'}
                </span>
              </li>
              <li>
                <span className="text-sm text-muted-foreground text-right block">
                  {dict.footer?.notFinancialAdvice || 'ليست نصيحة مالية'}
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-muted-foreground text-right w-full md:w-auto">
              © {currentYear} RibaCalc. {dict.footer?.allRightsReserved || 'جميع الحقوق محفوظة'}.
            </p>
            <p className="text-sm text-muted-foreground flex items-center gap-2">
              <span>{dict.footer?.forArabWorld || 'للعالم العربي'}</span>
              <Heart className="h-4 w-4 text-red-500" />
              <span>{dict.footer?.madeWith || 'صُنع بـ'}</span>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
