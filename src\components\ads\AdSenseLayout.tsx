"use client";

/**
 * تخطيطات الإعلانات المحسنة مع أفضل الممارسات
 * Enhanced ad layouts with best practices
 */

import { ReactNode, useMemo, memo } from 'react';
import AdSenseAd, { shouldShowAds, AdSafeZone, type AdSlotType } from './AdSenseAd';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import type { Locale } from '@/types';

/**
 * إعدادات مواضع الإعلانات
 * Ad placement settings
 */
interface AdPlacementSettings {
  readonly header?: boolean;
  readonly sidebar?: boolean;
  readonly content?: boolean;
  readonly footer?: boolean;
  readonly sticky?: boolean;
  readonly inline?: boolean;
}

/**
 * خصائص تخطيط الإعلانات
 * AdSense layout props
 */
interface AdSenseLayoutProps {
  readonly children: ReactNode;
  readonly lang?: Locale;
  readonly showAds?: boolean;
  readonly adPlacements?: AdPlacementSettings;
  readonly className?: string;
  readonly contentType?: 'article' | 'calculator' | 'page' | 'blog';
}

/**
 * التخطيط الرئيسي للإعلانات
 * Main ad layout component
 */
const AdSenseLayout = memo(function AdSenseLayout({
  children,
  lang = 'en',
  showAds = true,
  adPlacements = {
    header: true,
    sidebar: true,
    content: true,
    footer: false,
    sticky: true
  },
  className = '',
  contentType = 'page'
}: AdSenseLayoutProps) {
  const isMobile = useIsMobile();
  
  // تحسين التحقق من عرض الإعلانات
  const shouldDisplay = useMemo(() => {
    return showAds && shouldShowAds();
  }, [showAds]);

  // إعدادات الإعلانات المُحسنة حسب نوع المحتوى
  const optimizedPlacements = useMemo(() => {
    const basePlacements = { ...adPlacements };
    
    // تحسين المواضع حسب نوع المحتوى
    switch (contentType) {
      case 'calculator':
        return {
          ...basePlacements,
          sidebar: !isMobile && basePlacements.sidebar,
          sticky: isMobile && basePlacements.sticky
        };
      case 'article':
        return {
          ...basePlacements,
          content: true, // دائماً في المقالات
          inline: basePlacements.inline !== false
        };
      case 'blog':
        return {
          ...basePlacements,
          header: basePlacements.header !== false,
          footer: basePlacements.footer !== false
        };
      default:
        return basePlacements;
    }
  }, [adPlacements, contentType, isMobile]);

  if (!shouldDisplay) {
    return <div className={cn("content-only-layout", className)}>{children}</div>;
  }

  return (
    <AdSafeZone fallback={<div className={cn("content-only-layout", className)}>{children}</div>}>
      <div className={cn("adsense-layout", className)}>
        {/* Header Ad - Above the fold */}
        {optimizedPlacements.header && (
          <section className="header-ad-container mb-6" aria-label="Advertisement">
            <AdSenseAd
              slot="header-banner"
              className="max-w-full transition-opacity duration-300"
              lang={lang}
              priority="high"
              lazy={false}
            />
          </section>
        )}

        <div className={cn(
          "content-wrapper flex flex-col gap-6 transition-all duration-300",
          !isMobile && optimizedPlacements.sidebar && "lg:flex-row"
        )}>
          {/* Main Content Area */}
          <main className={cn(
            "main-content flex-1 min-w-0",
            contentType === 'calculator' && "calculator-content"
          )}>
            {children}
            
            {/* Content Ad - After main content */}
            {optimizedPlacements.content && (
              <section className="content-ad-container mt-8 mb-6" aria-label="Advertisement">
                <AdSenseAd
                  slot="content-banner"
                  className="max-w-full transition-opacity duration-300"
                  lang={lang}
                  priority="normal"
                />
              </section>
            )}
          </main>

          {/* Sidebar - Desktop only */}
          {!isMobile && optimizedPlacements.sidebar && (
            <aside className={cn(
              "sidebar w-80 flex-shrink-0 transition-all duration-300",
              lang === 'ar' && "order-first"
            )}>
              <div className="sticky top-4 space-y-6">
                {/* Sidebar Rectangle Ad */}
                <section aria-label="Sidebar Advertisement">
                  <AdSenseAd
                    slot="sidebar-rectangle"
                    className="transition-opacity duration-300"
                    lang={lang}
                    priority="normal"
                  />
                </section>
                
                {/* Additional sidebar content */}
                <div className="sidebar-content">
                  {/* يمكن إضافة محتوى إضافي هنا */}
                </div>
              </div>
            </aside>
          )}
        </div>

        {/* Footer Ad */}
        {optimizedPlacements.footer && (
          <section className="footer-ad-container mt-8 pt-6 border-t border-border" aria-label="Footer Advertisement">
            <AdSenseAd
              slot="content-banner"
              className="max-w-full transition-opacity duration-300"
              lang={lang}
              priority="low"
            />
          </section>
        )}

        {/* Mobile Sticky Ad - Mobile only */}
        {isMobile && optimizedPlacements.sticky && (
          <section
            className="fixed bottom-0 left-0 right-0 z-40 bg-background/95 backdrop-blur-sm border-t border-border shadow-lg transition-transform duration-300"
            aria-label="Mobile Sticky Advertisement"
          >
            <AdSenseAd
              slot="mobile-sticky"
              className="transition-opacity duration-300"
              lang={lang}
              priority="high"
              lazy={false}
            />
          </section>
        )}
      </div>
    </AdSafeZone>
  );
});

export default AdSenseLayout;

/**
 * خصائص تخطيط المقالات
 * Article layout props
 */
interface ArticleAdLayoutProps {
  readonly children: ReactNode;
  readonly lang?: Locale;
  readonly showAds?: boolean;
  readonly contentLength?: 'short' | 'medium' | 'long';
  readonly className?: string;
  readonly articleType?: 'blog' | 'news' | 'guide' | 'tutorial';
}

/**
 * تخطيط مخصص للمقالات والمدونات
 * Specialized layout for blog posts and articles
 */
export const ArticleAdLayout = memo(function ArticleAdLayout({
  children,
  lang = 'en',
  showAds = true,
  contentLength = 'medium',
  className = '',
  articleType = 'blog'
}: ArticleAdLayoutProps) {
  const isMobile = useIsMobile();
  
  // تحسين التحقق من عرض الإعلانات
  const shouldDisplay = useMemo(() => {
    return showAds && shouldShowAds();
  }, [showAds]);

  // استراتيجية الإعلانات حسب طول المحتوى
  const adStrategy = useMemo(() => {
    const baseStrategy = {
      header: true,
      sidebar: !isMobile,
      content: contentLength !== 'short',
      footer: contentLength === 'long',
      sticky: isMobile,
      inline: contentLength === 'long'
    };

    // تخصيص حسب نوع المقال
    switch (articleType) {
      case 'tutorial':
        return { ...baseStrategy, inline: true, content: true };
      case 'guide':
        return { ...baseStrategy, sidebar: !isMobile, footer: true };
      case 'news':
        return { ...baseStrategy, header: true, sticky: isMobile };
      default:
        return baseStrategy;
    }
  }, [contentLength, articleType, isMobile]);

  if (!shouldDisplay) {
    return (
      <div className={cn("article-content-only", className)}>
        {children}
      </div>
    );
  }

  return (
    <AdSafeZone fallback={
      <div className={cn("article-content-only", className)}>
        {children}
      </div>
    }>
      <div className={cn(
        "article-ad-layout",
        "transition-all duration-300 ease-in-out",
        className
      )}>
        {/* Header Ad - Above the fold */}
        {adStrategy.header && (
          <section
            className="header-ad-container mb-6 animate-in fade-in-0 duration-300"
            aria-label="مقال - إعلان علوي"
          >
            <AdSenseAd
              slot="header-banner"
              className="max-w-full transition-opacity duration-300"
              lang={lang}
              priority="high"
              lazy={false}
            />
          </section>
        )}

        <div className={cn(
          "article-wrapper flex flex-col gap-6 transition-all duration-300",
          !isMobile && adStrategy.sidebar && "lg:flex-row lg:gap-8"
        )}>
          {/* Article Content */}
          <article className={cn(
            "article-content flex-1 min-w-0",
            "prose prose-lg max-w-none",
            lang === 'ar' && "prose-arabic text-right"
          )}>
            {children}
            
            {/* Content Ad - After article content */}
            {adStrategy.content && (
              <section
                className="content-ad-container mt-8 mb-6 animate-in slide-in-from-bottom-5 duration-500"
                aria-label="مقال - إعلان المحتوى"
              >
                <AdSenseAd
                  slot="content-banner"
                  className="max-w-full transition-opacity duration-300"
                  lang={lang}
                  priority="normal"
                />
              </section>
            )}
          </article>

          {/* Article Sidebar - Desktop only */}
          {adStrategy.sidebar && (
            <aside className={cn(
              "article-sidebar w-80 flex-shrink-0",
              "transition-all duration-300 ease-in-out",
              lang === 'ar' && "order-first"
            )}>
              <div className="sticky top-4 space-y-6">
                {/* Sidebar Rectangle Ad */}
                <section
                  className="animate-in slide-in-from-right-5 duration-700"
                  aria-label="مقال - إعلان جانبي"
                >
                  <AdSenseAd
                    slot="sidebar-rectangle"
                    className="transition-opacity duration-300"
                    lang={lang}
                    priority="normal"
                  />
                </section>
                
                {/* Related Articles Section */}
                <div className="related-content bg-card rounded-lg p-4 border border-border">
                  <h3 className={cn(
                    "text-lg font-semibold mb-3",
                    lang === 'ar' && "text-right font-arabic"
                  )}>
                    {lang === 'ar' ? 'مقالات ذات صلة' :
                     lang === 'fr' ? 'Articles connexes' :
                     'Related Articles'}
                  </h3>
                  {/* يمكن إضافة قائمة المقالات ذات الصلة هنا */}
                </div>
              </div>
            </aside>
          )}
        </div>

        {/* Footer Ad - For long articles */}
        {adStrategy.footer && (
          <section
            className="footer-ad-container mt-8 pt-6 border-t border-border animate-in fade-in-0 duration-500"
            aria-label="مقال - إعلان سفلي"
          >
            <AdSenseAd
              slot="content-banner"
              className="max-w-full transition-opacity duration-300"
              lang={lang}
              priority="low"
            />
          </section>
        )}

        {/* Mobile Sticky Ad */}
        {adStrategy.sticky && (
          <section
            className={cn(
              "fixed bottom-0 left-0 right-0 z-40",
              "bg-background/95 backdrop-blur-sm border-t border-border shadow-lg",
              "transition-transform duration-300 ease-in-out"
            )}
            aria-label="إعلان ثابت للجوال"
          >
            <AdSenseAd
              slot="mobile-sticky"
              className="transition-opacity duration-300"
              lang={lang}
              priority="high"
              lazy={false}
            />
          </section>
        )}
      </div>
    </AdSafeZone>
  );
});

/**
 * خصائص الإعلان المضمن
 * Inline ad props
 */
interface InlineAdProps {
  readonly lang?: Locale;
  readonly className?: string;
  readonly showAds?: boolean;
  readonly variant?: 'standard' | 'responsive' | 'native';
  readonly spacing?: 'tight' | 'normal' | 'loose';
  readonly position?: 'start' | 'middle' | 'end';
}

/**
 * مكون إعلان مضمن للإدراج داخل المحتوى
 * Inline ad component for inserting ads within content
 */
export const InlineAd = memo(function InlineAd({
  lang = 'en',
  className = '',
  showAds = true,
  variant = 'standard',
  spacing = 'normal',
  position = 'middle'
}: InlineAdProps) {
  // تحسين التحقق من عرض الإعلانات
  const shouldDisplay = useMemo(() => {
    return showAds && shouldShowAds();
  }, [showAds]);

  // إعدادات التباعد
  const spacingClasses = useMemo(() => {
    switch (spacing) {
      case 'tight':
        return 'my-4';
      case 'loose':
        return 'my-12';
      default:
        return 'my-8';
    }
  }, [spacing]);

  // إعدادات الموضع
  const positionClasses = useMemo(() => {
    switch (position) {
      case 'start':
        return 'animate-in fade-in-0 slide-in-from-top-5 duration-500';
      case 'end':
        return 'animate-in fade-in-0 slide-in-from-bottom-5 duration-500';
      default:
        return 'animate-in fade-in-0 zoom-in-95 duration-300';
    }
  }, [position]);

  // استخدام نوع الإعلان المضمن فقط
  const adSlot: AdSlotType = 'article-inline';

  if (!shouldDisplay) {
    return null;
  }

  return (
    <AdSafeZone fallback={null}>
      <section
        className={cn(
          "inline-ad-container",
          spacingClasses,
          positionClasses,
          "flex justify-center items-center",
          "transition-all duration-300 ease-in-out",
          className
        )}
        aria-label={
          lang === 'ar' ? 'إعلان مضمن' :
          lang === 'fr' ? 'Publicité intégrée' :
          'Inline Advertisement'
        }
      >
        <div className={cn(
          "inline-ad-wrapper",
          "relative overflow-hidden rounded-lg",
          "border border-border/50",
          "bg-gradient-to-r from-background/50 to-muted/30",
          "backdrop-blur-sm",
          variant === 'native' && "bg-transparent border-none"
        )}>
          {/* إشارة الإعلان */}
          {variant !== 'native' && (
            <div className={cn(
              "ad-label absolute top-2 z-10",
              lang === 'ar' ? 'right-2' : 'left-2'
            )}>
              <span className="text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded-full">
                {lang === 'ar' ? 'إعلان' :
                 lang === 'fr' ? 'Pub' :
                 'Ad'}
              </span>
            </div>
          )}

          <AdSenseAd
            slot={adSlot}
            className={cn(
              "max-w-full transition-opacity duration-300",
              variant === 'responsive' && "w-full h-auto",
              variant === 'native' && "native-ad-style"
            )}
            lang={lang}
            priority="normal"
            lazy={position !== 'start'}
          />
        </div>
      </section>
    </AdSafeZone>
  );
});

/**
 * خصائص تخطيط الحاسبة
 * Calculator layout props
 */
interface CalculatorAdLayoutProps {
  readonly children: ReactNode;
  readonly lang?: Locale;
  readonly showAds?: boolean;
  readonly calculatorType?: 'loan' | 'mortgage' | 'investment' | 'savings' | 'compound';
  readonly className?: string;
  readonly showResults?: boolean;
}

/**
 * تخطيط مخصص للحاسبات المالية
 * Calculator-specific ad layout
 */
export const CalculatorAdLayout = memo(function CalculatorAdLayout({
  children,
  lang = 'en',
  showAds = true,
  calculatorType = 'loan',
  className = '',
  showResults = false
}: CalculatorAdLayoutProps) {
  const isMobile = useIsMobile();
  
  // تحسين التحقق من عرض الإعلانات
  const shouldDisplay = useMemo(() => {
    return showAds && shouldShowAds();
  }, [showAds]);

  // استراتيجية الإعلانات للحاسبات
  const calculatorAdStrategy = useMemo(() => {
    const baseStrategy = {
      header: true,
      sidebar: !isMobile,
      results: showResults,
      footer: calculatorType === 'compound' || calculatorType === 'investment',
      sticky: isMobile,
      tools: !isMobile
    };

    // تخصيص حسب نوع الحاسبة
    switch (calculatorType) {
      case 'loan':
        return { ...baseStrategy, results: true, tools: true };
      case 'mortgage':
        return { ...baseStrategy, sidebar: !isMobile, footer: true };
      case 'investment':
        return { ...baseStrategy, results: true, footer: true };
      case 'savings':
        return { ...baseStrategy, tools: true };
      default:
        return baseStrategy;
    }
  }, [calculatorType, isMobile, showResults]);

  if (!shouldDisplay) {
    return (
      <div className={cn("calculator-content-only", className)}>
        {children}
      </div>
    );
  }

  return (
    <AdSafeZone fallback={
      <div className={cn("calculator-content-only", className)}>
        {children}
      </div>
    }>
      <div className={cn(
        "calculator-ad-layout",
        "transition-all duration-300 ease-in-out",
        className
      )}>
        {/* Header Ad - Above calculator */}
        {calculatorAdStrategy.header && (
          <section
            className="header-ad-container mb-6 animate-in fade-in-0 duration-300"
            aria-label={
              lang === 'ar' ? 'حاسبة - إعلان علوي' :
              lang === 'fr' ? 'Calculatrice - Publicité d\'en-tête' :
              'Calculator - Header Advertisement'
            }
          >
            <AdSenseAd
              slot="header-banner"
              className="max-w-full transition-opacity duration-300"
              lang={lang}
              priority="high"
              lazy={false}
            />
          </section>
        )}

        <div className={cn(
          "calculator-wrapper flex flex-col gap-6 transition-all duration-300",
          !isMobile && calculatorAdStrategy.sidebar && "xl:flex-row xl:gap-8"
        )}>
          {/* Calculator Content */}
          <main className={cn(
            "calculator-content flex-1 min-w-0",
            "bg-card rounded-lg border border-border",
            "transition-all duration-300 ease-in-out"
          )}>
            {children}
            
            {/* Results Ad - After calculation results */}
            {calculatorAdStrategy.results && (
              <section
                className="results-ad-container mt-6 pt-6 border-t border-border animate-in slide-in-from-bottom-5 duration-500"
                aria-label={
                  lang === 'ar' ? 'حاسبة - إعلان النتائج' :
                  lang === 'fr' ? 'Calculatrice - Publicité des résultats' :
                  'Calculator - Results Advertisement'
                }
              >
                <AdSenseAd
                  slot="content-banner"
                  className="max-w-full transition-opacity duration-300"
                  lang={lang}
                  priority="normal"
                />
              </section>
            )}
          </main>

          {/* Calculator Sidebar - Large screens only */}
          {calculatorAdStrategy.sidebar && (
            <aside className={cn(
              "calculator-sidebar w-80 flex-shrink-0",
              "transition-all duration-300 ease-in-out",
              lang === 'ar' && "order-first"
            )}>
              <div className="sticky top-4 space-y-6">
                {/* Sidebar Rectangle Ad */}
                <section
                  className="animate-in slide-in-from-right-5 duration-700"
                  aria-label={
                    lang === 'ar' ? 'حاسبة - إعلان جانبي' :
                    lang === 'fr' ? 'Calculatrice - Publicité latérale' :
                    'Calculator - Sidebar Advertisement'
                  }
                >
                  <AdSenseAd
                    slot="sidebar-rectangle"
                    className="transition-opacity duration-300"
                    lang={lang}
                    priority="normal"
                  />
                </section>
                
                {/* Calculator Tools Section */}
                {calculatorAdStrategy.tools && (
                  <div className="calculator-tools bg-card rounded-lg p-4 border border-border">
                    <h3 className={cn(
                      "text-lg font-semibold mb-3",
                      lang === 'ar' && "text-right font-arabic"
                    )}>
                      {lang === 'ar' ? 'أدوات أخرى' :
                       lang === 'fr' ? 'Autres outils' :
                       'Other Tools'}
                    </h3>
                    <div className="space-y-2">
                      {/* يمكن إضافة روابط لحاسبات أخرى هنا */}
                      <div className="text-sm text-muted-foreground">
                        {lang === 'ar' ? 'حاسبات مالية أخرى' :
                         lang === 'fr' ? 'Autres calculatrices' :
                         'Related calculators'}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </aside>
          )}
        </div>

        {/* Footer Ad - For complex calculators */}
        {calculatorAdStrategy.footer && (
          <section
            className="footer-ad-container mt-8 pt-6 border-t border-border animate-in fade-in-0 duration-500"
            aria-label={
              lang === 'ar' ? 'حاسبة - إعلان سفلي' :
              lang === 'fr' ? 'Calculatrice - Publicité de pied de page' :
              'Calculator - Footer Advertisement'
            }
          >
            <AdSenseAd
              slot="content-banner"
              className="max-w-full transition-opacity duration-300"
              lang={lang}
              priority="low"
            />
          </section>
        )}

        {/* Mobile Sticky Ad */}
        {calculatorAdStrategy.sticky && (
          <section
            className={cn(
              "fixed bottom-0 left-0 right-0 z-40",
              "bg-background/95 backdrop-blur-sm border-t border-border shadow-lg",
              "transition-transform duration-300 ease-in-out"
            )}
            aria-label={
              lang === 'ar' ? 'إعلان ثابت للجوال' :
              lang === 'fr' ? 'Publicité collante mobile' :
              'Mobile Sticky Advertisement'
            }
          >
            <AdSenseAd
              slot="mobile-sticky"
              className="transition-opacity duration-300"
              lang={lang}
              priority="high"
              lazy={false}
            />
          </section>
        )}
      </div>
    </AdSafeZone>
  );
});