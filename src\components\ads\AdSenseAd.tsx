"use client";

/**
 * مكون الإعلانات المحسن مع أفضل الممارسات البرمجية
 * Enhanced AdSense component with best practices
 */

import { useEffect, useState, useCallback, memo, useMemo } from 'react';
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Eye, EyeOff } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Locale } from "@/types";

/**
 * إعدادات AdSense
 * AdSense configuration
 */
interface AdSenseConfig {
  readonly publisherId: string;
  readonly testMode: boolean;
  readonly enabled: boolean;
  readonly debug: boolean;
}

const ADSENSE_CONFIG: AdSenseConfig = {
  publisherId: process.env.NEXT_PUBLIC_ADSENSE_PUBLISHER_ID || 'ca-pub-XXXXXXXXXXXXXXXXX',
  testMode: process.env.NEXT_PUBLIC_ADSENSE_TEST_MODE === 'true' || process.env.NODE_ENV === 'development',
  enabled: process.env.NEXT_PUBLIC_ENABLE_ADSENSE !== 'false',
  debug: process.env.NEXT_PUBLIC_ADSENSE_DEBUG === 'true',
} as const;

/**
 * تكوين فتحات الإعلانات للمواضع المختلفة
 * Ad slot configurations for different placements
 */
interface AdSlotConfig {
  readonly slot: string;
  readonly format: 'auto' | 'rectangle' | 'fluid' | 'leaderboard';
  readonly responsive: boolean;
  readonly style: React.CSSProperties;
  readonly testStyle: React.CSSProperties;
  readonly description: string;
}

type AdSlotType = 'header-banner' | 'sidebar-rectangle' | 'content-banner' | 'mobile-sticky' | 'article-inline';

const AD_SLOTS: Record<AdSlotType, AdSlotConfig> = {
  'header-banner': {
    slot: process.env.NEXT_PUBLIC_ADSENSE_HEADER_SLOT || '1234567890',
    format: 'auto',
    responsive: true,
    style: { display: 'block', width: '100%', height: '90px' },
    testStyle: { minHeight: '90px' },
    description: 'Header banner ad for top-of-page placement'
  },
  'sidebar-rectangle': {
    slot: process.env.NEXT_PUBLIC_ADSENSE_SIDEBAR_SLOT || '2345678901',
    format: 'rectangle',
    responsive: true,
    style: { display: 'block', width: '300px', height: '250px' },
    testStyle: { width: '300px', height: '250px' },
    description: 'Sidebar rectangle ad for desktop view'
  },
  'content-banner': {
    slot: process.env.NEXT_PUBLIC_ADSENSE_CONTENT_SLOT || '3456789012',
    format: 'auto',
    responsive: true,
    style: { display: 'block', width: '100%', height: '280px' },
    testStyle: { minHeight: '280px' },
    description: 'Content banner ad for article placement'
  },
  'mobile-sticky': {
    slot: process.env.NEXT_PUBLIC_ADSENSE_MOBILE_SLOT || '4567890123',
    format: 'auto',
    responsive: true,
    style: { display: 'block', width: '100%', height: '50px' },
    testStyle: { minHeight: '50px' },
    description: 'Mobile sticky ad for bottom placement'
  },
  'article-inline': {
    slot: process.env.NEXT_PUBLIC_ADSENSE_INLINE_SLOT || '5678901234',
    format: 'fluid',
    responsive: true,
    style: { display: 'block', width: '100%' },
    testStyle: { minHeight: '200px' },
    description: 'Inline ad for insertion within article content'
  }
} as const;

/**
 * خصائص مكون الإعلان
 * AdSense ad component props
 */
interface AdSenseAdProps {
  readonly slot: AdSlotType;
  readonly className?: string;
  readonly lang?: Locale;
  readonly priority?: 'high' | 'normal' | 'low';
  readonly lazy?: boolean;
  readonly onLoad?: () => void;
  readonly onError?: (error: Error) => void;
}

/**
 * حالة الإعلان
 * Ad state interface
 */
interface AdState {
  readonly isLoaded: boolean;
  readonly hasError: boolean;
  readonly isLoading: boolean;
  readonly errorMessage?: string;
}

/**
 * تصريح gtag للتحليلات
 * gtag declaration for analytics
 */
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
    adsbygoogle?: any[];
  }
}

/**
 * مكون الإعلان المحسن
 * Enhanced AdSense component
 */
const AdSenseAd = memo(function AdSenseAd({
  slot,
  className = '',
  lang = 'en',
  priority = 'normal',
  lazy = true,
  onLoad,
  onError
}: AdSenseAdProps) {
  const [adState, setAdState] = useState<AdState>({
    isLoaded: false,
    hasError: false,
    isLoading: false
  });

  // تخزين مؤقت لإعدادات الإعلان
  const adConfig = useMemo(() => AD_SLOTS[slot], [slot]);

  /**
   * معالج تحميل الإعلان
   * Ad loading handler
   */
  const handleAdLoad = useCallback(() => {
    try {
      setAdState(prev => ({ ...prev, isLoaded: true, isLoading: false }));
      onLoad?.();
      
      if (ADSENSE_CONFIG.debug) {
        console.log(`AdSense: Ad loaded successfully for slot: ${slot}`);
      }

      // تتبع تحميل الإعلان
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'ad_loaded', {
          event_category: 'ads',
          event_label: slot,
          custom_parameter_priority: priority
        });
      }
    } catch (error) {
      console.error('AdSense load error:', error);
      handleAdError(new Error(`Failed to load ad for slot: ${slot}`));
    }
  }, [slot, priority, onLoad]);

  /**
   * معالج أخطاء الإعلان
   * Ad error handler
   */
  const handleAdError = useCallback((error: Error) => {
    setAdState(prev => ({
      ...prev,
      hasError: true,
      isLoading: false,
      errorMessage: error.message
    }));
    onError?.(error);
    
    if (ADSENSE_CONFIG.debug) {
      console.error(`AdSense: Error loading ad for slot ${slot}:`, error);
    }

    // تتبع أخطاء الإعلان
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'ad_error', {
        event_category: 'ads',
        event_label: slot,
        custom_parameter_error: error.message
      });
    }
  }, [slot, onError]);

  /**
   * تحميل سكريبت AdSense
   * Load AdSense script
   */
  const loadAdSenseScript = useCallback(async () => {
    if (typeof window === 'undefined') return;

    setAdState(prev => ({ ...prev, isLoading: true }));

    try {
      // تحقق من وجود السكريبت مسبقاً
      const existingScript = document.querySelector(
        `script[src*="pagead2.googlesyndication.com"]`
      );

      if (existingScript) {
        // إذا كان السكريبت موجود، فقط أضف الإعلان
        if (window.adsbygoogle) {
          window.adsbygoogle.push({});
          handleAdLoad();
        }
        return;
      }

      // إنشاء سكريبت جديد
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${ADSENSE_CONFIG.publisherId}`;
      script.crossOrigin = 'anonymous';
      
      script.onload = () => {
        try {
          window.adsbygoogle = window.adsbygoogle || [];
          window.adsbygoogle.push({});
          handleAdLoad();
        } catch (error) {
          handleAdError(new Error('Failed to initialize AdSense'));
        }
      };
      
      script.onerror = () => {
        handleAdError(new Error('Failed to load AdSense script'));
      };
      
      document.head.appendChild(script);
    } catch (error) {
      handleAdError(error instanceof Error ? error : new Error('Unknown AdSense error'));
    }
  }, [handleAdLoad, handleAdError]);

  // تأثير تحميل الإعلان
  useEffect(() => {
    // تحقق من إعدادات الإعلان العامة
    if (!ADSENSE_CONFIG.enabled) {
      if (ADSENSE_CONFIG.debug) {
        console.log('AdSense: Globally disabled via environment variable');
      }
      return;
    }

    if (!shouldShowAds() || !slot || ADSENSE_CONFIG.testMode) {
      if (ADSENSE_CONFIG.debug) {
        console.log('AdSense: Skipping ad load', {
          shouldShowAds: shouldShowAds(),
          slot,
          testMode: ADSENSE_CONFIG.testMode
        });
      }
      return;
    }

    // تحميل مؤجل أو فوري حسب الأولوية
    if (lazy && priority === 'low') {
      const timer = setTimeout(loadAdSenseScript, 1000);
      return () => clearTimeout(timer);
    } else {
      loadAdSenseScript();
      return undefined;
    }
  }, [slot, lazy, priority, loadAdSenseScript]);

  // عرض حالة الخطأ
  if (adState.hasError) {
    return (
      <div className={cn(
        "ad-container ad-error rounded-lg border border-red-200 bg-red-50",
        className
      )}>
        <Alert className="border-0 bg-transparent">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className={cn(
            "text-red-700 text-sm",
            lang === 'ar' && "font-arabic text-right"
          )}>
            {lang === 'ar' ? 'فشل في تحميل الإعلان' :
             lang === 'fr' ? 'Échec du chargement de l\'annonce' :
             'Failed to load ad'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // عرض حالة التحميل
  if (adState.isLoading) {
    return (
      <div className={cn("ad-container ad-loading", className)}>
        <Skeleton
          className="w-full rounded-lg"
          style={adConfig.testStyle}
        />
      </div>
    );
  }

  // عرض الإعلان الحقيقي
  return (
    <div className={cn(
      "ad-container ad-live transition-opacity duration-300",
      adState.isLoaded ? 'opacity-100' : 'opacity-0',
      className
    )}>
      <ins
        className="adsbygoogle"
        style={adConfig.style}
        data-ad-client={ADSENSE_CONFIG.publisherId}
        data-ad-slot={adConfig.slot}
        data-ad-format={adConfig.format}
        data-full-width-responsive={adConfig.responsive.toString()}
      />
    </div>
  );
});

export default AdSenseAd;

/**
 * دالة مساعدة للتحقق من إمكانية عرض الإعلانات
 * Utility function to check if ads should be shown
 */
export const shouldShowAds = (): boolean => {
  // عدم عرض الإعلانات في بيئة التطوير
  if (process.env.NODE_ENV === 'development') {
    return false;
  }
  
  // تحقق من إعدادات المتصفح
  if (typeof window !== 'undefined') {
    try {
      // تحقق من وجود ad blocker (فحص أساسي)
      const testAd = document.createElement('div');
      testAd.innerHTML = '&nbsp;';
      testAd.className = 'adsbox adUnit advertisement';
      testAd.style.cssText = 'position:absolute;left:-10000px;top:-1000px;width:1px;height:1px;';
      document.body.appendChild(testAd);
      
      const isBlocked = testAd.offsetHeight === 0 || testAd.clientHeight === 0;
      document.body.removeChild(testAd);
      
      if (isBlocked) {
        if (ADSENSE_CONFIG.debug) {
          console.log('AdSense: Ad blocker detected');
        }
        return false;
      }

      // تحقق من إعدادات الخصوصية
      const doNotTrack = navigator.doNotTrack === '1' ||
                        (window as any).doNotTrack === '1' ||
                        (navigator as any).msDoNotTrack === '1';
      
      if (doNotTrack) {
        if (ADSENSE_CONFIG.debug) {
          console.log('AdSense: Do Not Track enabled');
        }
        return false;
      }

    } catch (error) {
      console.error('AdSense: Error checking ad display conditions:', error);
      return false;
    }
  }
  
  return true;
};

/**
 * دالة مساعدة للحصول على معرف الإعلان
 * Get ad unit ID for specific slot
 */
export const getAdUnitId = (slot: AdSlotType): string => {
  return `${ADSENSE_CONFIG.publisherId}-${AD_SLOTS[slot].slot}`;
};

/**
 * دالة مساعدة للحصول على وصف الإعلان
 * Get ad description for accessibility
 */
export const getAdDescription = (slot: AdSlotType, lang: Locale): string => {
  const descriptions = {
    'header-banner': {
      ar: 'إعلان شريط علوي',
      en: 'Header banner advertisement',
      fr: 'Bannière publicitaire d\'en-tête'
    },
    'sidebar-rectangle': {
      ar: 'إعلان جانبي مربع',
      en: 'Sidebar rectangle advertisement',
      fr: 'Publicité rectangulaire latérale'
    },
    'content-banner': {
      ar: 'إعلان شريط المحتوى',
      en: 'Content banner advertisement',
      fr: 'Bannière publicitaire de contenu'
    },
    'mobile-sticky': {
      ar: 'إعلان ثابت للجوال',
      en: 'Mobile sticky advertisement',
      fr: 'Publicité collante mobile'
    },
    'article-inline': {
      ar: 'إعلان داخل المقال',
      en: 'Inline article advertisement',
      fr: 'Publicité intégrée à l\'article'
    }
  };

  return descriptions[slot][lang] || descriptions[slot]['en'];
};

/**
 * مكون منطقة آمنة للإعلانات
 * Safe ad zone component for error boundaries
 */
export const AdSafeZone = memo(function AdSafeZone({
  children,
  fallback
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode
}) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      if (event.message.includes('adsbygoogle') ||
          event.message.includes('googlesyndication')) {
        setHasError(true);
        console.error('AdSense error caught:', event.error);
      }
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <>{fallback || null}</>;
  }

  return <>{children}</>;
});

/**
 * تصدير أنواع البيانات
 * Export type definitions
 */
export type { AdSenseAdProps, AdSlotType, AdSlotConfig, AdState };

/**
 * تصدير الإعدادات
 * Export configurations
 */
export { ADSENSE_CONFIG, AD_SLOTS };