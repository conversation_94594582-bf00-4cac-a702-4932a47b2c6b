/**
 * الأنواع الأساسية للمشروع
 * Core types for the project
 */

// أنواع اللغات المدعومة
export type Locale = 'en' | 'fr' | 'ar';

// نوع بيانات العملة
export interface Currency {
  code: string;
  name: Record<Locale, string>;
  symbol: string;
  country: Record<Locale, string>;
  countryCode: string;
  locale: Record<Locale, string>;
}

// نوع بيانات حاسبة القرض
export interface LoanFormData {
  readonly loanAmount?: number;
  readonly interestRate?: number;
  readonly loanYears?: number;
}

// نتائج حساب القرض
export interface LoanCalculationResult {
  readonly principal: number;
  readonly totalInterest: number;
  readonly totalPayment: number;
  readonly monthlyPayment: number;
  readonly interestPercentage: number;
}

// نتائج حساب مدة القرض بناءً على القسط الشهري
export interface LoanDurationResult {
  readonly years: number;
  readonly months: number;
  readonly totalMonths: number;
  readonly totalPayment: number;
  readonly totalInterest: number;
  readonly interestPercentage: number;
  readonly endDate: Date;
}

// بيانات الرسم البياني
export interface ChartDataPoint {
  readonly name: string;
  readonly value: number;
  readonly fill: string;
}

// نوع مفاتيح القاموس
export interface DictionaryKeys {
  readonly header: {
    readonly title: string;
    readonly subtitle: string;
  };
  readonly calculator: {
    readonly title: string;
    readonly description: string;
    readonly loanAmount: string;
    readonly loanAmountPlaceholder: string;
    readonly interestRate: string;
    readonly interestRatePlaceholder: string;
    readonly loanTerm: string;
    readonly loanTermPlaceholder: string;
    readonly button: string;
    readonly buttonLoading: string;
    readonly currency: string;
  };
  readonly results: {
    readonly title: string;
    readonly description: string;
    readonly principal: string;
    readonly totalInterest: string;
    readonly totalRepayment: string;
    readonly monthlyPayment: string;
    readonly pieChartPrincipal: string;
    readonly pieChartTotalInterest: string;
    readonly placeholderTitle: string;
    readonly placeholderDescription: string;
  };
  readonly validation: {
    readonly loanAmountRequired: string;
    readonly loanAmountPositive: string;
    readonly interestRateRequired: string;
    readonly interestRateNonNegative: string;
    readonly interestRateMax: string;
    readonly loanYearsRequired: string;
    readonly loanYearsMinimum: string;
  };
  readonly navigation?: {
    readonly faq?: string;
  };
  readonly faq?: {
    readonly title?: string;
    readonly description?: string;
  };
  readonly quickAnswers?: {
    readonly title?: string;
    readonly description?: string;
    readonly whatIsRiba?: string;
    readonly whatIsRibaDescription?: string;
    readonly howFixedInterestCalculated?: string;
    readonly howFixedInterestCalculatedDescription?: string;
    readonly readAnswer?: string;
  };
  readonly regions?: {
    readonly international?: string;
    readonly gcc?: string;
    readonly maghreb?: string;
    readonly mashreq?: string;
    readonly other?: string;
  };
  readonly seo?: {
    readonly title?: string;
    readonly description?: string;
    readonly keywords?: string;
  };
  readonly warnings?: {
    readonly ribaWarningTitle?: string;
    readonly ribaWarningText?: string;
    readonly ribaWarningPercentage?: string;
    readonly ribaWarningVerse?: string;
    readonly islamicAlternativesTitle?: string;
    readonly murabaha?: string;
    readonly ijara?: string;
    readonly musharaka?: string;
  };
  readonly education?: {
    readonly ribaRisksTitle?: string;
    readonly ribaRisksContent1?: string;
    readonly ribaRisksContent2?: string;
    readonly ribaRisksContent3?: string;
    readonly faqQuestion1?: string;
    readonly faqAnswer1?: string;
    readonly faqQuestion2?: string;
    readonly faqAnswer2?: string;
    readonly faqQuestion3?: string;
    readonly faqAnswer3?: string;
  };
}

// نوع معالجة الأخطاء
export interface ErrorInfo {
  readonly message: string;
  readonly code?: string | undefined;
  readonly timestamp: Date;
}

// نوع مراجعة الكود
export interface ValidationError extends Error {
  readonly field: string;
  readonly value?: unknown;
}

// نوع الاستجابة من API
export interface ApiResponse<T = unknown> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: ErrorInfo;
}

// نوع بيانات SEO
export interface SEOData {
  readonly title: string;
  readonly description: string;
  readonly keywords?: string[];
  readonly canonical?: string;
  readonly locale: Locale;
}

// نوع Schema Markup
export interface SchemaMarkupData {
  readonly '@context': 'https://schema.org';
  readonly '@type': string;
  readonly name?: string;
  readonly description?: string;
  readonly url?: string;
  readonly image?: string;
  readonly applicationCategory?: string;
  readonly operatingSystem?: string;
  readonly offers?: {
    readonly '@type': 'Offer';
    readonly price: string;
    readonly priceCurrency: string;
  };
}

// نوع المراسة للأمان
export interface SecurityHeaders {
  readonly 'Content-Security-Policy': string;
  readonly 'X-Frame-Options': string;
  readonly 'X-Content-Type-Options': string;
  readonly 'Referrer-Policy': string;
  readonly 'Strict-Transport-Security': string;
}

// نوع تحسين الأداء
export interface PerformanceMetrics {
  readonly loadTime: number;
  readonly renderTime: number;
  readonly memoryUsage: number;
}