@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 208 100% 97%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 197 71% 60%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 120 73% 70%;
    --accent-foreground: 120 25% 15%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 197 71% 60%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 197 71% 73%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 120 73% 75%;
    --accent-foreground: 120 25% 15%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 197 71% 73%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Arabic font support - Enhanced for better readability */
  .font-arabic {
    font-family: 'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', 'Inter', system-ui, -apple-system, sans-serif;
    font-weight: 400;
    line-height: 1.75;
    letter-spacing: 0.02em;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Arabic headings - Improved hierarchy */
  .font-arabic h1, .font-arabic h2, .font-arabic h3, .font-arabic h4, .font-arabic h5, .font-arabic h6 {
    font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
    font-weight: 600;
    line-height: 1.4;
    letter-spacing: 0.01em;
  }

  .font-arabic h1 {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
  }

  .font-arabic h2 {
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.3;
  }

  .font-arabic h3 {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.4;
  }

  /* Arabic body text - Enhanced readability */
  .font-arabic p, .font-arabic span, .font-arabic div {
    line-height: 1.8;
    word-spacing: 0.15em;
  }

  /* Arabic display font for branding */
  .font-arabic-display {
    font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
    font-weight: 700;
    letter-spacing: 0.02em;
  }

  /* Arabic numbers - Using standard numerals */
  .font-arabic-numbers {
    font-family: 'monospace', 'Cairo', 'Noto Sans Arabic';
    font-feature-settings: 'tnum' 1;
    font-variant-numeric: lining-nums;
  }
}



/* RTL Support - Enhanced for Arabic */
[dir="rtl"] {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] .btn-icon-right {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* RTL specific adjustments - Fix flex direction issues */
[dir="rtl"] .flex:not(.flex-col):not(.flex-row) {
  flex-direction: row-reverse;
}

/* Enhanced spacing for RTL */
[dir="rtl"] .space-x-1 > * + * {
  margin-left: 0;
  margin-right: 0.25rem;
}

[dir="rtl"] .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .space-x-3 > * + * {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .space-x-6 > * + * {
  margin-left: 0;
  margin-right: 1.5rem;
}

/* Margin adjustments for RTL */
[dir="rtl"] .ml-1 {
  margin-left: 0;
  margin-right: 0.25rem;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-1 {
  margin-right: 0;
  margin-left: 0.25rem;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

/* Padding adjustments for RTL */
[dir="rtl"] .pl-2 {
  padding-left: 0;
  padding-right: 0.5rem;
}

[dir="rtl"] .pl-3 {
  padding-left: 0;
  padding-right: 0.75rem;
}

[dir="rtl"] .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

[dir="rtl"] .pr-2 {
  padding-right: 0;
  padding-left: 0.5rem;
}

[dir="rtl"] .pr-3 {
  padding-right: 0;
  padding-left: 0.75rem;
}

[dir="rtl"] .pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}

/* Form inputs RTL - Enhanced */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="email"],
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right;
  padding-left: 0.75rem;
  padding-right: 2.5rem;
  direction: rtl;
  font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
}

/* Special handling for number inputs in RTL */
[dir="rtl"] input[type="number"] {
  text-align: left !important;
  direction: ltr !important;
  padding-left: 2.5rem !important;
  padding-right: 0.75rem !important;
  font-family: monospace, 'Cairo', 'Noto Sans Arabic' !important;
  font-feature-settings: "tnum" !important;
  font-variant-numeric: lining-nums tabular-nums !important;
}

/* Enhanced form field spacing */
[dir="rtl"] .pl-10 {
  padding-left: 0.75rem;
  padding-right: 2.5rem;
}

[dir="rtl"] .pl-8 {
  padding-left: 0.75rem;
  padding-right: 2rem;
}

[dir="rtl"] .pr-3 {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

/* Button improvements for RTL */
[dir="rtl"] button {
  font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
}

/* Label alignment for RTL */
[dir="rtl"] label {
  text-align: right;
  display: block;
}

/* Icon positioning in RTL */
[dir="rtl"] .absolute.left-3 {
  left: auto;
  right: 0.75rem;
}

[dir="rtl"] .absolute.right-3 {
  right: auto;
  left: 0.75rem;
}

/* Special icon positioning for number inputs in RTL */
[dir="rtl"] input[type="number"] ~ .absolute.left-3,
[dir="rtl"] .input-wrapper:has(input[type="number"]) .absolute.left-3 {
  left: 0.75rem !important;
  right: auto !important;
}

/* Navigation RTL */
[dir="rtl"] .rotate-180 {
  transform: rotate(0deg);
}

[dir="rtl"] .rotate-0 {
  transform: rotate(180deg);
}

/* Grid and layout fixes for RTL */
[dir="rtl"] .grid {
  direction: rtl;
}

/* Mobile responsiveness for Arabic */
@media (max-width: 768px) {
  .font-arabic h1 {
    font-size: 2rem;
    line-height: 1.3;
  }

  .font-arabic h2 {
    font-size: 1.75rem;
    line-height: 1.4;
  }

  .font-arabic h3 {
    font-size: 1.25rem;
    line-height: 1.5;
  }

  .font-arabic p, .font-arabic span, .font-arabic div {
    font-size: 0.95rem;
    line-height: 1.7;
  }

  [dir="rtl"] input[type="text"],
  [dir="rtl"] input[type="email"],
  [dir="rtl"] textarea,
  [dir="rtl"] select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
  }
  
  /* Mobile-specific fixes for number inputs in RTL */
  [dir="rtl"] input[type="number"] {
    font-size: 16px; /* Prevents zoom on iOS */
    text-align: left !important;
    direction: ltr !important;
    padding-left: 2.5rem !important;
    padding-right: 0.75rem !important;
    font-family: 'Cairo', 'Noto Sans Arabic', monospace !important;
    font-feature-settings: "tnum" !important;
    font-variant-numeric: tabular-nums !important;
    -webkit-appearance: none !important;
    -moz-appearance: textfield !important;
    appearance: textfield !important;
  }

  /* Fix Arabic numbers and text alignment on mobile */
  [dir="rtl"] .arabic-numbers {
    direction: ltr !important;
    text-align: left !important;
    display: inline-block;
    font-family: monospace, 'Cairo', 'Noto Sans Arabic';
    font-weight: 600;
    unicode-bidi: embed;
    font-variant-numeric: lining-nums;
  }

  /* Fix flex layout on mobile for Arabic */
  [dir="rtl"] .flex.justify-between {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-direction: row !important;
    gap: 0.75rem !important;
  }

  /* Ensure proper text alignment on mobile */
  [dir="rtl"] .flex.justify-between > *:first-child {
    text-align: right !important;
    flex: 1 1 auto;
    min-width: 0;
  }

  [dir="rtl"] .flex.justify-between > *:last-child {
    text-align: left !important;
    flex: 0 0 auto;
    white-space: nowrap;
  }

  /* Fix card content spacing on mobile */
  [dir="rtl"] .w-full.space-y-3 > div {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    gap: 0.75rem !important;
    min-height: 2.5rem;
    padding: 0.5rem 0;
  }

  /* Prevent text overflow on mobile */
  [dir="rtl"] .flex-shrink-0 {
    flex-shrink: 0 !important;
    max-width: 45% !important;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Fix number display on mobile */
  [dir="rtl"] .arabic-numbers.text-left {
    direction: ltr !important;
    text-align: left !important;
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum";
  }
}

/* Accessibility improvements for Arabic */
[dir="rtl"] :focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Enhanced readability for Arabic text */
.font-arabic {
  font-variant-numeric: proportional-nums;
  font-feature-settings: 'kern' 1, 'liga' 1;
}

/* Background grid pattern */
.bg-grid-pattern {
  background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-in {
  animation: fade-in 0.6s ease-out;
}

.fade-in {
  animation-name: fade-in;
}

.duration-1000 {
  animation-duration: 1000ms;
}

/* Better contrast for Arabic text */
[dir="rtl"] .text-muted-foreground {
  color: hsl(var(--foreground) / 0.7);
}

/* Improved button spacing for Arabic */
[dir="rtl"] .btn-group > button:not(:last-child) {
  margin-left: 0;
  margin-right: 0.5rem;
}

/* Card improvements for RTL */
[dir="rtl"] .card {
  text-align: right;
}

[dir="rtl"] .card-header {
  text-align: right;
}

[dir="rtl"] .card-content {
  text-align: right;
}

[dir="rtl"] .card-footer {
  text-align: right;
  justify-content: flex-end;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* Header and navigation fixes */
[dir="rtl"] .justify-between {
  direction: rtl;
}

[dir="rtl"] .items-center {
  direction: rtl;
}

/* Arabic numbers styling - Using standard numerals */
.arabic-numbers {
  font-feature-settings: "tnum";
  font-variant-numeric: lining-nums tabular-nums;
  font-family: monospace, 'Cairo', 'Noto Sans Arabic';
  font-weight: 600;
  letter-spacing: 0.05em;
  direction: ltr !important;
  text-align: left !important;
  display: inline-block;
  unicode-bidi: embed;
}

/* Enhanced Arabic UI with standard numerals */
[dir="rtl"] .arabic-numbers {
  font-family: monospace, "Cairo", "IBM Plex Sans Arabic", "Noto Sans Arabic", sans-serif;
  font-weight: 600;
  direction: ltr !important;
  text-align: left !important;
  display: inline-block;
  unicode-bidi: embed;
  font-variant-numeric: lining-nums;
}

/* Force LTR for all number displays with standard numerals */
.arabic-numbers,
[dir="rtl"] .arabic-numbers {
  direction: ltr !important;
  text-align: left !important;
  unicode-bidi: embed;
  font-variant-numeric: lining-nums tabular-nums;
  font-feature-settings: "tnum";
  font-family: monospace, 'Cairo', 'Noto Sans Arabic' !important;
  white-space: nowrap !important;
}

/* RTL number input specific styles with standard numerals */
.rtl-number-input {
  direction: ltr !important;
  text-align: left !important;
  font-family: monospace, 'Cairo', 'Noto Sans Arabic' !important;
  font-feature-settings: "tnum" !important;
  font-variant-numeric: lining-nums tabular-nums !important;
}

/* Arabic text improvements */
[dir="rtl"] {
  font-size: 1.05em;
  line-height: 1.8;
}

/* Better spacing for Arabic cards */
[dir="rtl"] .card {
  padding: 1.5rem;
}

[dir="rtl"] .card-header {
  padding-bottom: 1.5rem;
}

[dir="rtl"] .card-content {
  padding-top: 0;
  padding-bottom: 1.5rem;
}

[dir="rtl"] .form-label {
  text-align: right;
  display: block;
  width: 100%;
}

[dir="rtl"] .input-wrapper {
  display: flex;
  flex-direction: row-reverse;
}

[dir="rtl"] .input-icon {
  margin-left: 0;
  margin-right: 8px;
}

/* Fix text alignment in cards for RTL */
[dir="rtl"] .text-left {
  text-align: right !important;
}

[dir="rtl"] .text-center {
  text-align: center !important;
}

/* Fix spacing and margins in RTL */
[dir="rtl"] .space-y-3 > * + * {
  margin-top: 0.75rem;
}

[dir="rtl"] .space-y-4 > * + * {
  margin-top: 1rem;
}

/* Fix button icons in RTL */
[dir="rtl"] .ml-2 {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

[dir="rtl"] .mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}

/* Fix card content alignment */
[dir="rtl"] .flex.justify-between {
  text-align: right;
}

[dir="rtl"] .flex.justify-between > *:first-child {
  text-align: right;
}

[dir="rtl"] .flex.justify-between > *:last-child {
  text-align: left;
}

/* Fix overlapping text in results - Enhanced */
[dir="rtl"] .flex.justify-between.items-center {
  gap: 1.5rem;
  min-height: 3rem;
  padding: 0.75rem 0;
}

[dir="rtl"] .flex-shrink-0 {
  flex-shrink: 0;
  max-width: 55%;
}

[dir="rtl"] .text-right {
  text-align: right !important;
  flex-shrink: 0;
  min-width: 40%;
  padding-left: 1rem;
}

/* Enhanced Arabic button styling */
[dir="rtl"] .btn {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
}

/* Arabic form improvements */
[dir="rtl"] .form-item {
  margin-bottom: 1.5rem;
}

[dir="rtl"] .form-label {
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-size: 1.1em;
}

[dir="rtl"] input, [dir="rtl"] select, [dir="rtl"] textarea {
  padding: 0.875rem 1rem;
  font-size: 1.05em;
  border-radius: 0.5rem;
  border: 2px solid hsl(var(--border));
  transition: all 0.2s ease;
}

[dir="rtl"] input:focus, [dir="rtl"] select:focus, [dir="rtl"] textarea:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
}

/* Ensure proper spacing in Arabic layout */
[dir="rtl"] .w-full.space-y-3 {
  width: 100%;
}

[dir="rtl"] .w-full.space-y-3 > div {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

/* Fix header text overlap */
[dir="rtl"] .text-4xl,
[dir="rtl"] .text-5xl {
  line-height: 1.2;
  word-spacing: 0.1em;
}

/* Ensure proper text wrapping */
[dir="rtl"] .max-w-xs,
[dir="rtl"] .max-w-sm,
[dir="rtl"] .max-w-md {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Fix layout stability - prevent width changes */
@layer utilities {
  .grid-stable {
    grid-template-columns: 1fr;
  }

  @media (min-width: 768px) {
    .grid-stable {
      grid-template-columns: 1fr 1fr;
    }
  }

  /* Ensure cards maintain consistent width */
  .card-stable {
    flex: 1 1 0%;
    min-width: 0;
    width: 100%;
  }

  /* Prevent layout shift during calculations */
  .results-stable {
    min-height: 550px;
    transition: none;
  }

  /* Stable container width */
  .container-stable {
    max-width: 56rem;
    min-width: 0;
    width: 100%;
  }

  /* Arabic font styling - Enhanced */
  .font-arabic {
    font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', Arial, sans-serif;
    direction: rtl;
    line-height: 1.8;
  }

  /* RTL specific styles - Enhanced */
  [dir="rtl"] {
    text-align: right;
  }

  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  /* Arabic typography improvements */
  [dir="rtl"] h1 {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
  }

  [dir="rtl"] h2 {
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.875rem;
  }

  [dir="rtl"] h3 {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.75rem;
  }

  /* Enhanced card styling for Arabic */
  [dir="rtl"] .card {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border-radius: 0.75rem;
    border: 1px solid hsl(var(--border));
  }

  [dir="rtl"] .card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
  }

  /* Better spacing for Arabic content */
  [dir="rtl"] .space-y-4 > * + * {
    margin-top: 1.25rem;
  }

  [dir="rtl"] .space-y-6 > * + * {
    margin-top: 1.75rem;
  }

  /* Arabic navigation improvements */
  [dir="rtl"] .nav-link {
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  [dir="rtl"] .nav-link:hover {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }

  /* Enhanced Arabic input styling with standard numerals */
  [dir="rtl"] input[type="number"] {
    text-align: left !important;
    direction: ltr !important;
    font-family: monospace, 'Cairo', 'Noto Sans Arabic' !important;
    font-weight: 600 !important;
    unicode-bidi: plaintext !important;
    -moz-appearance: textfield !important;
    appearance: textfield !important;
    font-variant-numeric: lining-nums !important;
  }

  [dir="rtl"] input[type="number"]::-webkit-inner-spin-button,
  [dir="rtl"] input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
  }

  [dir="rtl"] input[type="number"]::placeholder {
    text-align: right !important;
    direction: rtl !important;
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif !important;
    font-weight: 400 !important;
  }

  /* Arabic select styling */
  [dir="rtl"] select {
    padding-right: 2.5rem;
    padding-left: 1rem;
  }

  /* Enhanced Arabic card headers */
  [dir="rtl"] .card-header h3 {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1.3;
  }

  [dir="rtl"] .card-description {
    font-size: 1.125rem;
    line-height: 1.7;
    margin-top: 0.75rem;
  }

  /* Arabic button enhancements */
  [dir="rtl"] button {
    font-weight: 600;
    letter-spacing: 0.025em;
  }

  /* Arabic tooltip and dropdown improvements */
  [dir="rtl"] .tooltip, [dir="rtl"] .dropdown-content {
    text-align: right;
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
  }

  /* Enhanced Arabic spacing */
  [dir="rtl"] .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  @media (min-width: 640px) {
    [dir="rtl"] .container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* Arabic responsive improvements */
  @media (max-width: 768px) {
    [dir="rtl"] h1 {
      font-size: 2rem;
      line-height: 1.2;
    }

    [dir="rtl"] h2 {
      font-size: 1.75rem;
      line-height: 1.3;
    }

    [dir="rtl"] .card {
      padding: 1.25rem;
    }

    [dir="rtl"] .card-header {
      padding-bottom: 1.25rem;
    }

    /* Mobile-specific fixes for Arabic numbers with standard numerals */
    [dir="rtl"] .arabic-numbers {
      direction: ltr !important;
      text-align: left !important;
      display: inline-block !important;
      unicode-bidi: embed !important;
      font-variant-numeric: lining-nums tabular-nums !important;
      font-feature-settings: "tnum" !important;
      min-width: fit-content !important;
      font-family: monospace, 'Cairo', 'Noto Sans Arabic' !important;
    }

    /* Fix layout on small screens */
    [dir="rtl"] .flex.justify-between.items-center {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      flex-wrap: nowrap !important;
      gap: 0.5rem !important;
    }

    /* Ensure text doesn't overlap on mobile */
    [dir="rtl"] .flex.justify-between > span:first-child {
      flex: 1 1 auto;
      text-align: right !important;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      min-width: 0;
    }

    [dir="rtl"] .flex.justify-between > span:last-child {
      flex: 0 0 auto;
      text-align: left !important;
      white-space: nowrap;
      direction: ltr !important;
    }
  }

  /* Extra small screens */
  @media (max-width: 480px) {
    [dir="rtl"] .flex.justify-between {
      gap: 0.25rem !important;
    }

    [dir="rtl"] .flex-shrink-0 {
      max-width: 40% !important;
    }

    [dir="rtl"] .arabic-numbers {
      font-size: 0.9em !important;
    }
  }

  /* Arabic color enhancements */
  [dir="rtl"] .text-primary {
    color: hsl(var(--primary));
    font-weight: 600;
  }

  [dir="rtl"] .text-muted-foreground {
    color: hsl(var(--muted-foreground));
    opacity: 0.9;
  }

  /* Arabic focus states */
  [dir="rtl"] input:focus, [dir="rtl"] select:focus, [dir="rtl"] textarea:focus {
    outline: none;
    ring: 2px solid hsl(var(--primary));
    ring-offset: 2px;
    border-color: hsl(var(--primary));
  }

  /* Arabic hover states */
  [dir="rtl"] .card:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }

  [dir="rtl"] button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  /* Arabic loading states */
  [dir="rtl"] .loading {
    opacity: 0.7;
    pointer-events: none;
  }

  /* Arabic success/error states */
  [dir="rtl"] .success {
    background-color: hsl(var(--success) / 0.1);
    border: 1px solid hsl(var(--success));
    color: hsl(var(--success-foreground));
  }

  [dir="rtl"] .error {
    background-color: hsl(var(--destructive) / 0.1);
    border: 1px solid hsl(var(--destructive));
    color: hsl(var(--destructive-foreground));
  }

  /* Arabic animation improvements */
  [dir="rtl"] .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Arabic print styles */
  @media print {
    [dir="rtl"] {
      font-size: 12pt;
      line-height: 1.6;
    }

    [dir="rtl"] .card {
      break-inside: avoid;
      page-break-inside: avoid;
    }
  }
}

/* RTL Support for FAQ Lists */
[dir="rtl"] ul {
  list-style-position: inside;
  text-align: right;
}

[dir="rtl"] ul li {
  text-align: right;
  direction: rtl;
}

/* Ensure bullet points appear on the right for Arabic */
[dir="rtl"] ul li::marker {
  unicode-bidi: isolate;
  direction: rtl;
}

/* Custom bullet positioning for RTL */
.rtl-list {
  list-style: none;
  padding-right: 1.5rem;
  padding-left: 0;
}

.rtl-list li {
  position: relative;
  text-align: right;
  direction: rtl;
}

.rtl-list li::before {
  content: "•";
  position: absolute;
  right: -1.2rem;
  color: currentColor;
  font-weight: bold;
}

/* إخفاء روابط التذييل الإضافية */
footer a[href*="about"],
footer a[href*="contact"],
footer a[href*="privacy"] {
  display: none !important;
}
