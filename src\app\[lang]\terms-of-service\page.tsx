import { getDictionary } from '@/lib/dictionaries';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { FileText, AlertTriangle, Scale, Shield, Users, Gavel } from 'lucide-react';

type TermsOfServicePageProps = {
  params: Promise<{ lang: 'en' | 'fr' | 'ar' }>;
};

export async function generateMetadata({ params }: TermsOfServicePageProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);
  
  const titles = {
    en: 'Terms of Service | RibaCalc',
    fr: 'Conditions d\'Utilisation | RibaCalc',
    ar: 'شروط الخدمة | RibaCalc'
  };
  
  const descriptions = {
    en: 'Terms of service for RibaCalc loan calculator. Understand your rights and responsibilities when using our free financial tools.',
    fr: 'Conditions d\'utilisation pour la calculatrice de prêt RibaCalc. Comprenez vos droits et responsabilités.',
    ar: 'شروط الخدمة لحاسبة القروض RibaCalc. افهم حقوقك ومسؤولياتك عند استخدام أدواتنا المالية المجانية.'
  };

  return {
    title: titles[lang],
    description: descriptions[lang],
  };
}

export default async function TermsOfServicePage({ params }: TermsOfServicePageProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  const content = {
    en: {
      title: 'Terms of Service',
      lastUpdated: 'Last updated: December 2024',
      sections: [
        {
          icon: FileText,
          title: 'Acceptance of Terms',
          content: `By accessing and using RibaCalc, you accept and agree to be bound by the terms and provision of this agreement.
          
If you do not agree to abide by the above, please do not use this service.
          
These terms apply to all visitors, users, and others who access or use the service.`
        },
        {
          icon: Users,
          title: 'Use License',
          content: `Permission is granted to temporarily use RibaCalc for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:
          
• Modify or copy the materials
• Use the materials for any commercial purpose or for any public display
• Attempt to reverse engineer any software contained on the website
• Remove any copyright or other proprietary notations from the materials
          
This license shall automatically terminate if you violate any of these restrictions.`
        },
        {
          icon: AlertTriangle,
          title: 'Disclaimer and Limitations',
          content: `**Educational Purpose Only**: RibaCalc is provided for educational and informational purposes only. All calculations are estimates and should not be considered as financial advice.
          
**No Financial Advice**: We do not provide financial, investment, or legal advice. Always consult with qualified professionals before making financial decisions.
          
**Accuracy**: While we strive for accuracy, we make no warranties about the completeness, reliability, or accuracy of the calculations or information.
          
**Religious Considerations**: Our Islamic finance warnings are for educational purposes. Consult with qualified Islamic scholars for religious guidance.`
        },
        {
          icon: Shield,
          title: 'Prohibited Uses',
          content: `You may not use our service:
          
• For any unlawful purpose or to solicit others to unlawful acts
• To violate any international, federal, provincial, or state regulations, rules, laws, or local ordinances
• To infringe upon or violate our intellectual property rights or the intellectual property rights of others
• To harass, abuse, insult, harm, defame, slander, disparage, intimidate, or discriminate
• To submit false or misleading information
• To upload viruses or any other type of malicious code
• To spam, phish, pharm, pretext, spider, crawl, or scrape
• For any obscene or immoral purpose
• To interfere with or circumvent the security features of the service`
        },
        {
          icon: Scale,
          title: 'Limitation of Liability',
          content: `In no event shall RibaCalc or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on RibaCalc's website.
          
Because some jurisdictions do not allow limitations on implied warranties, or limitations of liability for consequential or incidental damages, these limitations may not apply to you.
          
**Maximum Liability**: Our total liability to you for all damages, losses, and causes of action shall not exceed the amount paid by you, if any, for accessing this site.`
        },
        {
          icon: Gavel,
          title: 'Governing Law and Changes',
          content: `**Governing Law**: These terms and conditions are governed by and construed in accordance with the laws of Morocco and you irrevocably submit to the exclusive jurisdiction of the courts in that State or location.
          
**Modifications**: RibaCalc may revise these terms of service at any time without notice. By using this website, you are agreeing to be bound by the then current version of these terms of service.
          
**Severability**: If any provision of these Terms is held to be invalid or unenforceable, the remaining provisions will remain in full force and effect.
          
**Contact**: For questions about these Terms of Service, please contact us through our website.`
        }
      ]
    },
    fr: {
      title: 'Conditions d\'Utilisation',
      lastUpdated: 'Dernière mise à jour : Décembre 2024',
      sections: [
        {
          icon: FileText,
          title: 'Acceptation des Conditions',
          content: `En accédant et en utilisant RibaCalc, vous acceptez et convenez d'être lié par les termes et dispositions de cet accord.
          
Si vous n'acceptez pas de respecter ce qui précède, veuillez ne pas utiliser ce service.
          
Ces conditions s'appliquent à tous les visiteurs, utilisateurs et autres qui accèdent ou utilisent le service.`
        },
        {
          icon: Users,
          title: 'Licence d\'Utilisation',
          content: `L'autorisation est accordée d'utiliser temporairement RibaCalc pour un usage personnel, non commercial et transitoire uniquement. Il s'agit de l'octroi d'une licence, non d'un transfert de titre, et sous cette licence vous ne pouvez pas :
          
• Modifier ou copier les matériaux
• Utiliser les matériaux à des fins commerciales ou pour tout affichage public
• Tenter d'effectuer de la rétro-ingénierie sur tout logiciel contenu sur le site web
• Supprimer tout droit d'auteur ou autres mentions de propriété des matériaux
          
Cette licence se terminera automatiquement si vous violez l'une de ces restrictions.`
        },
        {
          icon: AlertTriangle,
          title: 'Avertissement et Limitations',
          content: `**But Éducatif Uniquement** : RibaCalc est fourni à des fins éducatives et informatives uniquement. Tous les calculs sont des estimations et ne doivent pas être considérés comme des conseils financiers.
          
**Aucun Conseil Financier** : Nous ne fournissons pas de conseils financiers, d'investissement ou juridiques. Consultez toujours des professionnels qualifiés avant de prendre des décisions financières.
          
**Précision** : Bien que nous nous efforcions d'être précis, nous ne garantissons pas l'exhaustivité, la fiabilité ou la précision des calculs ou informations.
          
**Considérations Religieuses** : Nos avertissements sur la finance islamique sont à des fins éducatives. Consultez des érudits islamiques qualifiés pour des conseils religieux.`
        },
        {
          icon: Shield,
          title: 'Utilisations Interdites',
          content: `Vous ne pouvez pas utiliser notre service :
          
• À des fins illégales ou pour solliciter d'autres à des actes illégaux
• Pour violer des réglementations, règles, lois internationales, fédérales, provinciales ou d'état
• Pour enfreindre nos droits de propriété intellectuelle ou ceux d'autrui
• Pour harceler, abuser, insulter, nuire, diffamer, calomnier, dénigrer, intimider ou discriminer
• Pour soumettre des informations fausses ou trompeuses
• Pour télécharger des virus ou tout autre type de code malveillant
• Pour spammer, hameçonner, pharmer, prétexte, explorer, ramper ou gratter
• À des fins obscènes ou immorales
• Pour interférer avec ou contourner les fonctionnalités de sécurité du service`
        },
        {
          icon: Scale,
          title: 'Limitation de Responsabilité',
          content: `En aucun cas RibaCalc ou ses fournisseurs ne seront responsables de tout dommage (y compris, sans limitation, les dommages pour perte de données ou de profit, ou dus à une interruption d'activité) découlant de l'utilisation ou de l'incapacité d'utiliser les matériaux sur le site web de RibaCalc.
          
Parce que certaines juridictions n'autorisent pas les limitations sur les garanties implicites, ou les limitations de responsabilité pour les dommages consécutifs ou accessoires, ces limitations peuvent ne pas s'appliquer à vous.
          
**Responsabilité Maximale** : Notre responsabilité totale envers vous pour tous dommages, pertes et causes d'action ne dépassera pas le montant payé par vous, le cas échéant, pour accéder à ce site.`
        },
        {
          icon: Gavel,
          title: 'Droit Applicable et Modifications',
          content: `**Droit Applicable** : Ces conditions générales sont régies et interprétées conformément aux lois du Maroc et vous vous soumettez irrévocablement à la juridiction exclusive des tribunaux de cet État ou lieu.
          
**Modifications** : RibaCalc peut réviser ces conditions de service à tout moment sans préavis. En utilisant ce site web, vous acceptez d'être lié par la version alors actuelle de ces conditions de service.
          
**Divisibilité** : Si une disposition de ces Conditions est jugée invalide ou inapplicable, les dispositions restantes resteront pleinement en vigueur.
          
**Contact** : Pour des questions sur ces Conditions de Service, veuillez nous contacter via notre site web.`
        }
      ]
    },
    ar: {
      title: 'شروط الخدمة',
      lastUpdated: 'آخر تحديث: ديسمبر 2024',
      sections: [
        {
          icon: FileText,
          title: 'قبول الشروط',
          content: `بالوصول إلى RibaCalc واستخدامه، فإنك تقبل وتوافق على الالتزام بشروط وأحكام هذه الاتفاقية.
          
إذا كنت لا توافق على الالتزام بما سبق، يرجى عدم استخدام هذه الخدمة.
          
تنطبق هذه الشروط على جميع الزوار والمستخدمين وغيرهم ممن يصلون إلى الخدمة أو يستخدمونها.`
        },
        {
          icon: Users,
          title: 'رخصة الاستخدام',
          content: `يُمنح الإذن لاستخدام RibaCalc مؤقتاً للعرض الشخصي غير التجاري المؤقت فقط. هذا منح رخصة وليس نقل ملكية، وتحت هذه الرخصة لا يجوز لك:
          
• تعديل أو نسخ المواد
• استخدام المواد لأي غرض تجاري أو لأي عرض عام
• محاولة الهندسة العكسية لأي برنامج موجود على الموقع
• إزالة أي حقوق طبع ونشر أو إشعارات ملكية أخرى من المواد
          
ستنتهي هذه الرخصة تلقائياً إذا انتهكت أي من هذه القيود.`
        },
        {
          icon: AlertTriangle,
          title: 'إخلاء المسؤولية والقيود',
          content: `**للأغراض التعليمية فقط**: يتم توفير RibaCalc للأغراض التعليمية والإعلامية فقط. جميع الحسابات تقديرية ولا ينبغي اعتبارها نصائح مالية.
          
**لا نصائح مالية**: نحن لا نقدم نصائح مالية أو استثمارية أو قانونية. استشر دائماً المهنيين المؤهلين قبل اتخاذ القرارات المالية.
          
**الدقة**: بينما نسعى للدقة، لا نقدم ضمانات حول اكتمال أو موثوقية أو دقة الحسابات أو المعلومات.
          
**الاعتبارات الدينية**: تحذيراتنا حول التمويل الإسلامي هي لأغراض تعليمية. استشر علماء إسلاميين مؤهلين للإرشاد الديني.`
        },
        {
          icon: Shield,
          title: 'الاستخدامات المحظورة',
          content: `لا يجوز لك استخدام خدمتنا:
          
• لأي غرض غير قانوني أو لحث الآخرين على أعمال غير قانونية
• لانتهاك أي لوائح أو قواعد أو قوانين دولية أو فيدرالية أو إقليمية أو محلية
• لانتهاك حقوق الملكية الفكرية الخاصة بنا أو بالآخرين
• للمضايقة أو الإساءة أو الإهانة أو الإضرار أو التشهير أو القذف أو التمييز
• لتقديم معلومات كاذبة أو مضللة
• لتحميل فيروسات أو أي نوع آخر من الأكواد الضارة
• للرسائل المزعجة أو التصيد أو الاحتيال أو الزحف أو الكشط
• لأي غرض فاحش أو غير أخلاقي
• للتدخل في أو تجاوز ميزات الأمان للخدمة`
        },
        {
          icon: Scale,
          title: 'تحديد المسؤولية',
          content: `لن يكون RibaCalc أو مورديه مسؤولين في أي حال عن أي أضرار (بما في ذلك، دون حصر، الأضرار لفقدان البيانات أو الربح، أو بسبب انقطاع الأعمال) الناشئة عن استخدام أو عدم القدرة على استخدام المواد على موقع RibaCalc.
          
لأن بعض الولايات القضائية لا تسمح بقيود على الضمانات الضمنية، أو قيود المسؤولية عن الأضرار التبعية أو العرضية، قد لا تنطبق عليك هذه القيود.
          
**المسؤولية القصوى**: مسؤوليتنا الإجمالية تجاهك عن جميع الأضرار والخسائر وأسباب الدعوى لن تتجاوز المبلغ المدفوع منك، إن وجد، للوصول إلى هذا الموقع.`
        },
        {
          icon: Gavel,
          title: 'القانون الحاكم والتغييرات',
          content: `**القانون الحاكم**: تحكم هذه الشروط والأحكام وتفسر وفقاً لقوانين المغرب وأنت تخضع بشكل لا رجعة فيه للاختصاص الحصري لمحاكم تلك الدولة أو الموقع.
          
**التعديلات**: قد يراجع RibaCalc شروط الخدمة هذه في أي وقت دون إشعار. باستخدام هذا الموقع، فإنك توافق على الالتزام بالنسخة الحالية من شروط الخدمة هذه.
          
**القابلية للفصل**: إذا تبين أن أي حكم من هذه الشروط غير صالح أو غير قابل للتنفيذ، ستبقى الأحكام المتبقية سارية المفعول بالكامل.
          
**الاتصال**: للأسئلة حول شروط الخدمة هذه، يرجى الاتصال بنا عبر موقعنا.`
        }
      ]
    }
  };

  const pageContent = content[lang];

  return (
    <div className={`container mx-auto px-4 py-8 max-w-4xl ${lang === 'ar' ? 'font-arabic' : ''}`}>
      <div className="text-center mb-8">
        <h1 className={`text-3xl md:text-4xl font-bold text-primary mb-4 ${lang === 'ar' ? 'font-arabic-display text-4xl md:text-5xl' : ''}`}>
          {pageContent.title}
        </h1>
        <p className="text-muted-foreground">{pageContent.lastUpdated}</p>
      </div>

      <div className="space-y-6">
        {pageContent.sections.map((section, index) => {
          const IconComponent = section.icon;
          return (
            <Card key={index}>
              <CardHeader>
                <CardTitle className={`flex items-center gap-3 ${lang === 'ar' ? 'font-arabic text-xl' : ''}`}>
                  <IconComponent className="h-6 w-6 text-primary" />
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={`whitespace-pre-line text-muted-foreground leading-relaxed ${lang === 'ar' ? 'font-arabic text-lg leading-loose text-right' : ''}`}>
                  {section.content}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <Separator className="my-8" />
      
      <div className={`text-center text-sm text-muted-foreground ${lang === 'ar' ? 'font-arabic text-base' : ''}`}>
        <p>
          {lang === 'ar' 
            ? 'هذه الشروط قابلة للتغيير. استخدامك المستمر للموقع يعني موافقتك على أي تغييرات.'
            : lang === 'fr'
            ? 'Ces conditions peuvent être modifiées. Votre utilisation continue du site signifie votre acceptation de tout changement.'
            : 'These terms may be updated. Your continued use of the site means your acceptance of any changes.'}
        </p>
      </div>
    </div>
  );
}