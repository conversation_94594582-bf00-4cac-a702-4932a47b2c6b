'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { calculateLoanDuration } from '@/lib/utils';
import { getCurrencyList, formatCurrency, getCurrencySymbol } from '@/lib/currency';
import type { LoanDurationResult, Currency, DictionaryKeys, Locale } from '@/types';
import { cn } from '@/lib/utils';

interface LoanDurationForm {
  loanAmount: string;
  interestRate: string;
  monthlyPayment: string;
  currency: string;
}

interface LoanDurationCalculatorProps {
  dict: DictionaryKeys;
  lang: Locale;
}

export default function LoanDurationCalculator({ dict, lang }: LoanDurationCalculatorProps) {
  // التحقق من وجود القاموس
  if (!dict) {
    return <div>Loading...</div>;
  }
  const [formData, setFormData] = useState<LoanDurationForm>({
    loanAmount: '',
    interestRate: '',
    monthlyPayment: '',
    currency: 'MAD'
  });
  
  const [result, setResult] = useState<LoanDurationResult | null>(null);
  const [error, setError] = useState<string>('');
  const [isCalculating, setIsCalculating] = useState(false);

  const currencies = getCurrencyList();

  const handleInputChange = (field: keyof LoanDurationForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // مسح الأخطاء والنتائج عند تغيير المدخلات
    if (error) setError('');
    if (result) setResult(null);
  };

  const validateForm = (): string | null => {
    const loanAmount = parseFloat(formData.loanAmount);
    const interestRate = parseFloat(formData.interestRate);
    const monthlyPayment = parseFloat(formData.monthlyPayment);

    if (!formData.loanAmount || isNaN(loanAmount) || loanAmount <= 0) {
      return dict.loanDuration?.errorInvalidAmount || 'Please enter a valid loan amount';
    }

    if (!formData.interestRate || isNaN(interestRate) || interestRate < 0) {
      return dict.loanDuration?.errorInvalidRate || 'Please enter a valid interest rate';
    }

    if (!formData.monthlyPayment || isNaN(monthlyPayment) || monthlyPayment <= 0) {
      return dict.loanDuration?.errorInvalidPayment || 'Please enter a valid monthly payment';
    }

    return null;
  };

  const handleCalculate = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsCalculating(true);
    setError('');

    try {
      const loanAmount = parseFloat(formData.loanAmount);
      const interestRate = parseFloat(formData.interestRate);
      const monthlyPayment = parseFloat(formData.monthlyPayment);

      const calculationResult = calculateLoanDuration(loanAmount, monthlyPayment, interestRate);
      
      if (!calculationResult) {
        setError(dict.loanDuration?.errorInsufficientPayment || 'Monthly payment is insufficient to pay off the loan. Please increase the monthly payment.');
        return;
      }

      setResult(calculationResult);
    } catch (err) {
      setError(dict.loanDuration?.errorCalculation || 'An error occurred in the calculation. Please try again.');
    } finally {
      setIsCalculating(false);
    }
  };

  const formatCurrencyAmount = (amount: number) => {
    return formatCurrency(amount, formData.currency, 'ar');
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('ar-MA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  const selectedCurrency = currencies.find(c => c.code === formData.currency);
  const currencySymbol = getCurrencySymbol(formData.currency);

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* حاسبة مدة القرض */}
      <Card className="w-full shadow-lg border-2 hover:shadow-xl transition-shadow">
        <CardHeader className="text-center bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <CardTitle className={cn(
            "text-2xl md:text-3xl font-bold text-blue-900 mb-2",
            lang === 'ar' && "font-arabic-display"
          )}>
            🕒 {dict.loanDuration?.title || 'Loan Duration Calculator'}
          </CardTitle>
          <CardDescription className={cn(
            "text-gray-600 text-base md:text-lg",
            lang === 'ar' && "font-arabic"
          )}>
            {dict.loanDuration?.description || 'Enter loan amount, interest rate, and monthly payment to find out when your loan will end'}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="p-4 md:p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
            
            {/* العمود الأول - المدخلات */}
            <div className="space-y-4">
              <h3 className={cn(
                "text-lg font-semibold text-gray-800 mb-4 flex items-center justify-center",
                lang === 'ar' && "font-arabic-display"
              )}>
                📝 {dict.loanDuration?.inputsTitle || 'Loan Details'}
              </h3>

              {/* اختيار العملة */}
              <div className="space-y-2">
                <Label htmlFor="currency" className={cn(
                  "text-sm font-medium text-gray-700 flex items-center",
                  lang === 'ar' && "font-arabic"
                )}>
                  💱 {dict.loanDuration?.currency || 'Currency'}
                </Label>
                <Select 
                  value={formData.currency} 
                  onValueChange={(value) => handleInputChange('currency', value)}
                >
                  <SelectTrigger className="w-full h-12 text-right">
                    <SelectValue placeholder={dict.loanDuration?.currencyPlaceholder || "Select currency"} />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.code} value={currency.code} className="text-right">
                        <div className="flex items-center justify-between w-full">
                          <span className="text-sm text-gray-500">{currency.symbol}</span>
                          <span className="mr-2">{currency.name.ar}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* مبلغ القرض */}
              <div className="space-y-2">
                <Label htmlFor="loanAmount" className={cn(
                  "text-sm font-medium text-gray-700 flex items-center",
                  lang === 'ar' && "font-arabic"
                )}>
                  💰 {dict.loanDuration?.loanAmount || 'Loan Amount'} ({currencySymbol})
                </Label>
                <div className="relative">
                  <Input
                    id="loanAmount"
                    type="number"
                    placeholder={dict.loanDuration?.loanAmountPlaceholder || "100000"}
                    value={formData.loanAmount}
                    onChange={(e) => handleInputChange('loanAmount', e.target.value)}
                    className="text-right h-12 pr-12 text-lg"
                    min="0"
                    step="1000"
                  />
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                    {currencySymbol}
                  </span>
                </div>
              </div>

              {/* معدل الفائدة */}
              <div className="space-y-2">
                <Label htmlFor="interestRate" className={cn(
                  "text-sm font-medium text-gray-700 flex items-center",
                  lang === 'ar' && "font-arabic"
                )}>
                  📈 {dict.loanDuration?.interestRate || 'Annual Interest Rate (%)'}
                </Label>
                <div className="relative">
                  <Input
                    id="interestRate"
                    type="number"
                    placeholder={dict.loanDuration?.interestRatePlaceholder || "8"}
                    value={formData.interestRate}
                    onChange={(e) => handleInputChange('interestRate', e.target.value)}
                    className="text-right h-12 pr-12 text-lg"
                    min="0"
                    max="100"
                    step="0.1"
                  />
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                    %
                  </span>
                </div>
              </div>

              {/* القسط الشهري */}
              <div className="space-y-2">
                <Label htmlFor="monthlyPayment" className={cn(
                  "text-sm font-medium text-gray-700 flex items-center",
                  lang === 'ar' && "font-arabic"
                )}>
                  💳 {dict.loanDuration?.monthlyPayment || 'Monthly Payment'} ({currencySymbol})
                </Label>
                <div className="relative">
                  <Input
                    id="monthlyPayment"
                    type="number"
                    placeholder={dict.loanDuration?.monthlyPaymentPlaceholder || "2500"}
                    value={formData.monthlyPayment}
                    onChange={(e) => handleInputChange('monthlyPayment', e.target.value)}
                    className="text-right h-12 pr-12 text-lg"
                    min="0"
                    step="100"
                  />
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                    {currencySymbol}
                  </span>
                </div>
              </div>

              {/* زر الحساب */}
              <Button
                onClick={handleCalculate}
                disabled={isCalculating}
                className={cn(
                  "w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 h-12 text-lg",
                  lang === 'ar' && "font-arabic"
                )}
              >
                {isCalculating ?
                  `⏳ ${dict.loanDuration?.calculating || 'Calculating...'}` :
                  `🧮 ${dict.loanDuration?.calculateButton || 'Calculate Loan Duration'}`
                }
              </Button>
            </div>

            {/* العمود الثاني - النتائج */}
            <div className="space-y-4">
              {/* عرض الأخطاء */}
              {error && (
                <Alert className="border-red-200 bg-red-50">
                  <AlertDescription className="text-red-700">
                    ⚠️ {error}
                  </AlertDescription>
                </Alert>
              )}

              {/* عرض النتائج */}
              {result ? (
                <div className="space-y-4">
                  <h3 className={cn(
                    "text-lg font-semibold text-green-800 mb-4 flex items-center justify-center",
                    lang === 'ar' && "font-arabic-display"
                  )}>
                    📊 {dict.loanDuration?.resultsTitle || 'Loan Duration Results'}
                  </h3>

                  <div className="grid grid-cols-1 gap-3">
                    {/* المدة المتبقية */}
                    <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                      <div className={cn(
                        "text-xl md:text-2xl font-bold text-blue-600",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {result.years} {dict.loanDuration?.years || 'years'} {dict.loanDuration?.and || 'and'} {result.months} {dict.loanDuration?.months || 'months'}
                      </div>
                      <div className={cn(
                        "text-gray-600 mt-1 text-sm",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {dict.loanDuration?.duration || 'Loan Duration'}
                      </div>
                    </div>

                    {/* إجمالي الشهور */}
                    <div className="text-center p-4 bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-lg border border-indigo-200">
                      <div className={cn(
                        "text-xl md:text-2xl font-bold text-indigo-600",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {result.totalMonths} {dict.loanDuration?.months || 'months'}
                      </div>
                      <div className={cn(
                        "text-gray-600 mt-1 text-sm",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {dict.loanDuration?.totalMonths || 'Total Months'}
                      </div>
                    </div>

                    {/* تاريخ الانتهاء */}
                    <div className="text-center p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
                      <div className={cn(
                        "text-base md:text-lg font-bold text-green-600",
                        lang === 'ar' && "font-arabic"
                      )}>
                        📅 {formatDate(result.endDate)}
                      </div>
                      <div className={cn(
                        "text-gray-600 mt-1 text-sm",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {dict.loanDuration?.loanEndDate || 'Loan End Date'}
                      </div>
                    </div>

                    {/* إجمالي المبلغ المدفوع */}
                    <div className="text-center p-4 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg border border-orange-200">
                      <div className={cn(
                        "text-base md:text-lg font-bold text-orange-600",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {formatCurrencyAmount(result.totalPayment)}
                      </div>
                      <div className={cn(
                        "text-gray-600 mt-1 text-sm",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {dict.loanDuration?.totalAmount || 'Total Amount Paid'}
                      </div>
                    </div>

                    {/* إجمالي الفوائد */}
                    <div className="text-center p-4 bg-gradient-to-r from-red-50 to-red-100 rounded-lg border border-red-200">
                      <div className={cn(
                        "text-base md:text-lg font-bold text-red-600",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {formatCurrencyAmount(result.totalInterest)}
                      </div>
                      <div className={cn(
                        "text-gray-600 mt-1 text-sm",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {dict.loanDuration?.totalInterest || 'Total Interest'}
                      </div>
                    </div>

                    {/* نسبة الفائدة */}
                    <div className="text-center p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                      <div className={cn(
                        "text-base md:text-lg font-bold text-purple-600",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {result.interestPercentage.toFixed(1)}%
                      </div>
                      <div className={cn(
                        "text-gray-600 mt-1 text-sm",
                        lang === 'ar' && "font-arabic"
                      )}>
                        {dict.loanDuration?.interestPercentageLabel || 'Interest Percentage of Principal'}
                      </div>
                    </div>
                  </div>

                  {/* تحذير من الربا */}
                  {result.totalInterest > 0 && (
                    <Alert className="border-red-300 bg-red-50 mt-4">
                      <AlertDescription className={cn(
                        "text-red-700 text-center text-sm",
                        lang === 'ar' && "font-arabic"
                      )}>
                        ⚠️ {(dict.loanDuration?.ribaWarning || 'Warning: This loan contains interest (riba) of {amount}').replace('{amount}', formatCurrencyAmount(result.totalInterest))}
                        <br />
                        {dict.loanDuration?.ribaWarningNote || 'Interest is prohibited in Islam. Consider Sharia-compliant financing alternatives.'}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                /* رسالة الانتظار */
                <div className="text-center p-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <div className="text-4xl mb-4">🧮</div>
                  <h3 className={cn(
                    "text-lg font-semibold text-gray-700 mb-2",
                    lang === 'ar' && "font-arabic-display"
                  )}>
                    {dict.loanDuration?.waitingTitle || 'Waiting for Data'}
                  </h3>
                  <p className={cn(
                    "text-gray-600 text-sm",
                    lang === 'ar' && "font-arabic"
                  )}>
                    {dict.loanDuration?.waitingDescription || 'Enter loan details above and click "Calculate" to see results'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}