"use client";

/**
 * مكون محدد العملة المحسن مع أفضل الممارسات البرمجية
 * Enhanced currency selector component with best practices
 */

import { useState, useEffect, useCallback, useMemo } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, Banknote, MapPin, AlertCircle } from "lucide-react";
import { getCurrencyList, getCurrency, type Currency } from "@/lib/currency";
import { smartCurrencyDetection } from "@/lib/geolocation";
import type { DictionaryKeys, Locale } from "@/types";
import { STORAGE_KEYS } from "@/constants";
import { createErrorInfo, cn } from "@/lib/utils";

/**
 * خصائص مكون محدد العملة
 * Currency selector component props
 */
interface CurrencySelectorProps {
  readonly selectedCurrency: string;
  readonly onCurrencyChange: (currencyCode: string) => void;
  readonly lang: Locale;
  readonly dict: DictionaryKeys;
  readonly autoDetect?: boolean;
  readonly disabled?: boolean;
  readonly className?: string;
}

/**
 * حالة اكتشاف العملة
 * Currency detection state
 */
interface DetectionState {
  readonly isDetecting: boolean;
  readonly detectedCurrency: string | null;
  readonly detectionFailed: boolean;
  readonly error: string | null;
}

/**
 * مجموعة العملات المنطقية
 * Logical currency group
 */
interface CurrencyGroup {
  readonly title: string;
  readonly currencies: readonly Currency[];
}

/**
 * مكون محدد العملة الرئيسي مع أفضل الممارسات
 * Main currency selector component with best practices
 */
export default function CurrencySelector({
  selectedCurrency,
  onCurrencyChange,
  lang,
  dict,
  autoDetect = true,
  disabled = false,
  className = "",
}: CurrencySelectorProps) {
  // الحالة المحلية للمكون
  const [detectionState, setDetectionState] = useState<DetectionState>({
    isDetecting: false,
    detectedCurrency: null,
    detectionFailed: false,
    error: null,
  });
  
  const [isOpen, setIsOpen] = useState(false);

  // الحصول على البيانات مع التخزين المؤقت
  const currencies = useMemo(() => getCurrencyList(), []);
  const currentCurrency = useMemo(() => getCurrency(selectedCurrency), [selectedCurrency]);

  /**
   * معالج إغلاق القائمة
   * Close handler
   */
  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  /**
   * معالج تغيير العملة
   * Currency change handler
   */
  const handleCurrencyChange = useCallback((currencyCode: string) => {
    try {
      onCurrencyChange(currencyCode);
      handleClose();
    } catch (error) {
      console.error('Error changing currency:', error);
      setDetectionState(prev => ({
        ...prev,
        error: 'فشل في تغيير العملة',
      }));
    }
  }, [onCurrencyChange, handleClose]);

  /**
   * اكتشاف العملة التلقائي
   * Auto currency detection
   */
  useEffect(() => {
    const isGeolocationDisabled = process.env.NEXT_PUBLIC_DISABLE_GEOLOCATION === 'true';

    if (autoDetect && !detectionState.detectedCurrency && !detectionState.detectionFailed && !isGeolocationDisabled) {
      setDetectionState(prev => ({ ...prev, isDetecting: true, error: null }));

      // مهلة زمنية لمنع التحميل المستمر
      const detectionTimeout = setTimeout(() => {
        console.warn('Currency detection timeout, using default currency');
        setDetectionState({
          isDetecting: false,
          detectedCurrency: 'MAD',
          detectionFailed: true,
          error: null,
        });
      }, 10000); // مهلة 10 ثوانٍ

      smartCurrencyDetection()
        .then((currency) => {
          clearTimeout(detectionTimeout);
          setDetectionState({
            isDetecting: false,
            detectedCurrency: currency,
            detectionFailed: false,
            error: null,
          });
          
          // تعيين العملة تلقائياً فقط إذا لم يختر المستخدم عملة بعد
          if (selectedCurrency === 'MAD') {
            onCurrencyChange(currency);
          }
          console.log(`Currency auto-detected: ${currency}`);
        })
        .catch((error) => {
          clearTimeout(detectionTimeout);
          console.warn('Currency detection failed, using default:', error.message || error);
          setDetectionState({
            isDetecting: false,
            detectedCurrency: 'MAD',
            detectionFailed: true,
            error: lang === 'ar' ? 'فشل اكتشاف الموقع' : 'Location detection failed',
          });
          
          // استخدام العملة الافتراضية
          if (selectedCurrency === 'MAD') {
            onCurrencyChange('MAD');
          }
        });
    } else if (isGeolocationDisabled) {
      console.info('Geolocation disabled via environment variable, using default currency');
      setDetectionState({
        isDetecting: false,
        detectedCurrency: 'MAD',
        detectionFailed: false,
        error: null,
      });
      
      if (selectedCurrency === 'MAD') {
        onCurrencyChange('MAD');
      }
    }
  }, [autoDetect, detectionState.detectedCurrency, detectionState.detectionFailed, selectedCurrency, onCurrencyChange, lang]);

  // تنظيم العملات حسب المنطقة
  const currencyGroups: CurrencyGroup[] = useMemo(() => [
    {
      title: dict.regions?.international || "International Currencies",
      currencies: currencies.filter(c => ['USD', 'EUR'].includes(c.code)),
    },
    {
      title: dict.regions?.gcc || "Gulf Cooperation Council",
      currencies: currencies.filter(c => ['AED', 'SAR', 'KWD', 'QAR', 'BHD', 'OMR'].includes(c.code)),
    },
    {
      title: dict.regions?.maghreb || "Maghreb",
      currencies: currencies.filter(c => ['MAD', 'TND', 'DZD', 'LYD'].includes(c.code)),
    },
    {
      title: dict.regions?.mashreq || "Mashreq",
      currencies: currencies.filter(c => ['EGP', 'JOD', 'LBP', 'SYP'].includes(c.code)),
    },
    {
      title: dict.regions?.other || "Other Arab Countries",
      currencies: currencies.filter(c => ['IQD', 'YER', 'SDG'].includes(c.code)),
    },
  ], [currencies, dict.regions]);

  /**
   * تحويل كود البلد إلى علم
   * Convert country code to flag emoji
   */
  const getCountryFlag = useCallback((countryCode: string): string => {
    try {
      if (!countryCode || countryCode.length !== 2) {
        return '🏳️';
      }
      
      const codePoints = countryCode
        .toUpperCase()
        .split('')
        .map(char => 127397 + char.charCodeAt(0));
      return String.fromCodePoint(...codePoints);
    } catch (error) {
      console.warn('Error generating flag emoji:', error);
      return '🏳️';
    }
  }, []);

  /**
   * مكون مجموعة العملات
   * Currency group component
   */
  const CurrencyGroup = useCallback(({ title, currencies }: CurrencyGroup) => {
    if (currencies.length === 0) return null;

    return (
      <>
        <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground border-b">
          {title}
        </div>
        {currencies.map((currency) => (
          <DropdownMenuItem
            key={currency.code}
            onClick={() => handleCurrencyChange(currency.code)}
            className={cn(
              "flex items-center gap-3 px-3 py-2 cursor-pointer transition-colors",
              selectedCurrency === currency.code && "bg-accent",
              lang === 'ar' && "flex-row-reverse"
            )}
          >
            <span className="text-lg">
              {getCountryFlag(currency.countryCode)}
            </span>
            <div className="flex-1 min-w-0">
              <div className={cn(
                "flex items-center gap-2",
                lang === 'ar' && "flex-row-reverse"
              )}>
                <span className={cn(
                  "font-medium text-sm",
                  lang === 'ar' && "font-arabic"
                )}>
                  {currency.name[lang]}
                </span>
                <Badge variant="outline" className="text-xs">
                  {currency.code}
                </Badge>
              </div>
              <div className={cn(
                "text-xs text-muted-foreground truncate",
                lang === 'ar' && "font-arabic text-right"
              )}>
                {currency.country[lang]}
              </div>
            </div>
            <span className="text-sm font-mono text-muted-foreground">
              {currency.symbol}
            </span>
          </DropdownMenuItem>
        ))}
      </>
    );
  }, [selectedCurrency, lang, handleCurrencyChange, getCountryFlag]);

  return (
    <div className={cn("w-full", className)}>
      <label className={cn(
        "block text-sm font-medium mb-2",
        lang === 'ar' && "font-arabic text-right"
      )}>
        {dict.calculator?.currency || 'Currency'}
      </label>
      
      <DropdownMenu modal={false} open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-between h-12 px-4 transition-all",
              lang === 'ar' && "flex-row-reverse",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            dir={lang === 'ar' ? 'rtl' : 'ltr'}
            disabled={detectionState.isDetecting || disabled}
          >
            <div className={cn(
              "flex items-center gap-3",
              lang === 'ar' && "flex-row-reverse"
            )}>
              {detectionState.isDetecting ? (
                <MapPin className="h-4 w-4 text-muted-foreground animate-pulse" />
              ) : detectionState.error ? (
                <AlertCircle className="h-4 w-4 text-destructive" />
              ) : (
                <Banknote className="h-4 w-4 text-muted-foreground" />
              )}
              
              {detectionState.isDetecting ? (
                <span className={cn(
                  "text-sm text-muted-foreground",
                  lang === 'ar' && "font-arabic"
                )}>
                  {lang === 'ar' ? 'جاري تحديد موقعك...' :
                   lang === 'fr' ? 'Détection de votre localisation...' :
                   'Detecting your location...'}
                </span>
              ) : detectionState.error ? (
                <span className={cn(
                  "text-sm text-destructive",
                  lang === 'ar' && "font-arabic"
                )}>
                  {detectionState.error}
                </span>
              ) : currentCurrency ? (
                <>
                  <span className="text-lg">
                    {getCountryFlag(currentCurrency.countryCode)}
                  </span>
                  <div className={cn(
                    "flex items-center gap-2",
                    lang === 'ar' && "flex-row-reverse"
                  )}>
                    <span className={cn(
                      "font-medium",
                      lang === 'ar' && "font-arabic"
                    )}>
                      {currentCurrency.name[lang]}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {currentCurrency.code}
                    </Badge>
                  </div>
                </>
              ) : (
                <span className={cn(
                  "text-sm text-muted-foreground",
                  lang === 'ar' && "font-arabic"
                )}>
                  {lang === 'ar' ? 'اختر العملة' :
                   lang === 'fr' ? 'Sélectionner la devise' :
                   'Select Currency'}
                </span>
              )}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent
          className="w-80 max-h-96 overflow-y-auto"
          align={lang === 'ar' ? 'end' : 'start'}
        >
          {currencyGroups.map((group, index) => (
            <CurrencyGroup
              key={index}
              title={group.title}
              currencies={group.currencies}
            />
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      
      {/* رسالة الخطأ */}
      {detectionState.error && (
        <div className={cn(
          "mt-2 text-xs text-destructive",
          lang === 'ar' && "font-arabic text-right"
        )}>
          {detectionState.error}
        </div>
      )}
    </div>
  );
}
