/**
 * مكتبة الوظائف المساعدة المحسنة
 * Enhanced utility functions library
 */

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import type { Locale, ErrorInfo, ValidationError, LoanDurationResult } from "@/types";
import { VALIDATION_PATTERNS, ERROR_MESSAGES } from "@/constants";

/**
 * دمج أسماء الفئات مع دعم Tailwind
 * Merge class names with Tailwind support
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

/**
 * التحقق من صحة البريد الإلكتروني
 * Validate email address
 */
export function isValidEmail(email: string): boolean {
  if (typeof email !== 'string' || email.length === 0) {
    return false;
  }
  return VALIDATION_PATTERNS.EMAIL.test(email.trim());
}

/**
 * التحقق من صحة كود العملة
 * Validate currency code
 */
export function isValidCurrencyCode(code: string): boolean {
  if (typeof code !== 'string') {
    return false;
  }
  return VALIDATION_PATTERNS.CURRENCY_CODE.test(code.toUpperCase());
}

/**
 * التحقق من صحة الرقم الموجب
 * Validate positive number
 */
export function isPositiveNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value) && value > 0 && isFinite(value);
}

/**
 * التحقق من صحة النطاق
 * Validate number range
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return isPositiveNumber(value) && value >= min && value <= max;
}

/**
 * تنظيف النص من المحتوى الضار
 * Sanitize text from harmful content
 */
export function sanitizeText(text: string): string {
  if (typeof text !== 'string') {
    return '';
  }
  
  return text
    .trim()
    .replace(/[<>'"&]/g, (char) => {
      const entities: Record<string, string> = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '&': '&amp;',
      };
      return entities[char] || char;
    });
}

/**
 * تنسيق الأرقام بطريقة آمنة
 * Format numbers safely
 */
export function safeFormatNumber(
  value: unknown,
  locale: Locale = 'en',
  options: Intl.NumberFormatOptions = {}
): string {
  if (!isPositiveNumber(value as number)) {
    return '0';
  }

  try {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
      ...options,
    }).format(value as number);
  } catch (error) {
    console.warn('Number formatting failed:', error);
    return String(value);
  }
}

/**
 * إنشاء معرف فريد آمن
 * Generate secure unique identifier
 */
export function generateSecureId(prefix: string = ''): string {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 15);
  return `${prefix}${timestamp}_${randomPart}`;
}

/**
 * تأخير التنفيذ (debounce)
 * Debounce function execution
 */
export function debounce<T extends (...args: any[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * تقييد معدل التنفيذ (throttle)
 * Throttle function execution
 */
export function throttle<T extends (...args: any[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}

/**
 * إنشاء معلومات خطأ منظمة
 * Create structured error information
 */
export function createErrorInfo(
  message: string,
  code?: string | undefined,
  originalError?: Error
): ErrorInfo {
  return {
    message: sanitizeText(message),
    code: code ?? undefined,
    timestamp: new Date(),
  };
}

/**
 * إنشاء خطأ تحقق منظم
 * Create structured validation error
 */
export function createValidationError(
  field: string,
  message: string,
  value?: unknown
): ValidationError {
  const error = new Error(message) as ValidationError;
  (error as any).field = field;
  (error as any).value = value;
  return error;
}

/**
 * التحقق من صحة البيانات المدخلة
 * Validate input data
 */
export function validateLoanInput(data: {
  loanAmount?: number;
  interestRate?: number;
  loanYears?: number;
}): ValidationError[] {
  const errors: ValidationError[] = [];

  // التحقق من مبلغ القرض
  if (data.loanAmount === undefined || !isPositiveNumber(data.loanAmount)) {
    errors.push(createValidationError(
      'loanAmount',
      ERROR_MESSAGES.INVALID_INPUT,
      data.loanAmount
    ));
  }

  // التحقق من معدل الفائدة
  if (data.interestRate === undefined ||
      typeof data.interestRate !== 'number' ||
      isNaN(data.interestRate) ||
      data.interestRate < 0 ||
      data.interestRate > 100) {
    errors.push(createValidationError(
      'interestRate',
      ERROR_MESSAGES.INVALID_INPUT,
      data.interestRate
    ));
  }

  // التحقق من مدة القرض
  if (data.loanYears === undefined ||
      !Number.isInteger(data.loanYears) ||
      data.loanYears < 1) {
    errors.push(createValidationError(
      'loanYears',
      ERROR_MESSAGES.INVALID_INPUT,
      data.loanYears
    ));
  }

  return errors;
}

/**
 * حساب القرض بطريقة آمنة باستخدام الفائدة المركبة شهرياً (الطريقة القياسية)
 * Calculate loan safely using standard monthly compound interest
 * 
 * الطريقة القياسية:
 * 1. حساب معدل الفائدة الشهري وعدد الأشهر الإجمالي
 * 2. تطبيق صيغة القرض القياسية لحساب الدفعة الشهرية
 * 
 * Standard method:
 * 1. Calculate monthly interest rate and total number of months
 * 2. Apply the standard loan formula to calculate the monthly payment
 */
export function calculateLoanSafely(
  principal: number,
  annualRate: number,
  years: number
): {
  principal: number;
  monthlyPayment: number;
  totalPayment: number;
  totalInterest: number;
  interestPercentage: number;
} | null {
  try {
    // التحقق من صحة المدخلات
    if (!isPositiveNumber(principal) ||
        typeof annualRate !== 'number' ||
        !Number.isInteger(years) ||
        years < 1) {
      return null;
    }

    const monthlyRate = annualRate / 100 / 12;
    const totalMonths = years * 12;

    let monthlyPayment: number;
    let totalPayment: number;
    let totalInterest: number;

    if (monthlyRate === 0) {
      // بدون فوائد
      monthlyPayment = principal / totalMonths;
      totalPayment = principal;
      totalInterest = 0;
    } else {
      // مع فوائد - صيغة الإطفاء الشهرية القياسية
      // M = P * [r(1+r)^n] / [(1+r)^n - 1]
      const factor = Math.pow(1 + monthlyRate, totalMonths);
      monthlyPayment = principal * (monthlyRate * factor) / (factor - 1);
      totalPayment = monthlyPayment * totalMonths;
      totalInterest = totalPayment - principal;
    }

    const interestPercentage = principal > 0 ? (totalInterest / principal) * 100 : 0;

    return {
      principal: Math.round(principal * 100) / 100,
      monthlyPayment: Math.round(monthlyPayment * 100) / 100,
      totalPayment: Math.round(totalPayment * 100) / 100,
      totalInterest: Math.round(totalInterest * 100) / 100,
      interestPercentage: Math.round(interestPercentage * 10) / 10,
    };
  } catch (error) {
    console.error('Loan calculation error:', error);
    return null;
  }
}

/**
 * حساب مدة القرض بناءً على القسط الشهري والفائدة
 * Calculate loan duration based on monthly payment and interest rate
 * 
 * @param principal مبلغ القرض الأساسي
 * @param monthlyPayment القسط الشهري المحدد
 * @param annualRate معدل الفائدة السنوي
 * @returns معلومات مدة القرض أو null في حالة الخطأ
 */
export function calculateLoanDuration(
  principal: number,
  monthlyPayment: number,
  annualRate: number
): {
  years: number;
  months: number;
  totalMonths: number;
  totalPayment: number;
  totalInterest: number;
  interestPercentage: number;
  endDate: Date;
} | null {
  try {
    // التحقق من صحة المدخلات
    if (!isPositiveNumber(principal) ||
        !isPositiveNumber(monthlyPayment) ||
        typeof annualRate !== 'number' ||
        annualRate < 0) {
      return null;
    }

    // حساب معدل الفائدة الشهري
    const monthlyRate = annualRate / 100 / 12;
    
    if (monthlyRate === 0) {
      // بدون فوائد - حساب بسيط
      const totalMonths = Math.ceil(principal / monthlyPayment);
      const years = Math.floor(totalMonths / 12);
      const months = totalMonths % 12;
      
      const totalPayment = totalMonths * monthlyPayment;
      const totalInterest = 0;
      const interestPercentage = 0;
      
      const currentDate = new Date();
      const endDate = new Date(currentDate);
      endDate.setMonth(endDate.getMonth() + totalMonths);
      
      return {
        years,
        months,
        totalMonths,
        totalPayment: Math.round(totalPayment * 100) / 100,
        totalInterest,
        interestPercentage,
        endDate
      };
    }

    // التحقق من أن القسط الشهري أكبر من الفائدة الشهرية فقط
    const monthlyInterestOnly = principal * monthlyRate;
    if (monthlyPayment <= monthlyInterestOnly) {
      // القسط غير كافي لسداد حتى الفوائد الشهرية
      return null;
    }

    // حساب عدد الأشهر باستخدام المعادلة الصحيحة للقروض
    // n = -ln(1 - (P * r) / PMT) / ln(1 + r)
    // حيث P = المبلغ الأساسي، r = معدل الفائدة الشهري، PMT = الدفعة الشهرية
    
    const numerator = (principal * monthlyRate) / monthlyPayment;
    
    if (numerator >= 1) {
      // القسط غير كافي لسداد حتى الفوائد
      return null;
    }
    
    const totalMonthsExact = -Math.log(1 - numerator) / Math.log(1 + monthlyRate);
    const totalMonths = Math.ceil(totalMonthsExact);

    const years = Math.floor(totalMonths / 12);
    const months = totalMonths % 12;

    // Recalculate total payment for better accuracy
    const fullPayments = Math.floor(totalMonthsExact);
    const finalPayment = (totalMonthsExact - fullPayments) * monthlyPayment;
    const totalPayment = (fullPayments * monthlyPayment) + finalPayment;

    const totalInterest = totalPayment - principal;
    const interestPercentage = principal > 0 ? (totalInterest / principal) * 100 : 0;
    
    // حساب تاريخ انتهاء القرض
    const currentDate = new Date();
    const endDate = new Date(currentDate);
    endDate.setMonth(endDate.getMonth() + totalMonths);
    
    return {
      years,
      months,
      totalMonths,
      totalPayment: Math.round(totalPayment * 100) / 100,
      totalInterest: Math.round(totalInterest * 100) / 100,
      interestPercentage: Math.round(interestPercentage * 10) / 10,
      endDate
    };
  } catch (error) {
    console.error('Loan duration calculation error:', error);
    return null;
  }
}

/**
 * حساب جدول سداد القرض
 * Calculate loan amortization schedule
 * 
 * @param principal مبلغ القرض الأساسي
 * @param annualRate معدل الفائدة السنوي
 * @param years مدة القرض بالسنوات
 * @returns جدول السداد أو null في حالة الخطأ
 */
export function calculateAmortizationSchedule(
  principal: number,
  annualRate: number,
  years: number
): Array<{
  month: number;
  interestPayment: number;
  principalPayment: number;
  remainingBalance: number;
}> | null {
  try {
    const loanData = calculateLoanSafely(principal, annualRate, years);

    if (!loanData) {
      return null; // تعذّر حساب بيانات القرض الأساسية
    }

    const { monthlyPayment } = loanData;
    const monthlyRate = annualRate / 100 / 12;
    const totalMonths = years * 12;
    const schedule: Array<{
      month: number;
      interestPayment: number;
      principalPayment: number;
      remainingBalance: number;
    }> = [];
    let remainingBalance = principal;

    for (let month = 1; month <= totalMonths; month++) {
      if (remainingBalance <= 0.01) break; // التوقف إذا كان الرصيد صغيرًا جدًا

      const interestPayment = remainingBalance * monthlyRate;
      let principalPayment = monthlyPayment - interestPayment;
      
      // التأكد من أن الدفعة الأخيرة لا تتجاوز الرصيد المتبقي
      if (remainingBalance < monthlyPayment) {
        principalPayment = remainingBalance;
        remainingBalance = 0;
      } else {
        remainingBalance -= principalPayment;
      }

      schedule.push({
        month,
        interestPayment: Math.max(0, interestPayment),
        principalPayment: Math.max(0, principalPayment),
        remainingBalance: Math.max(0, remainingBalance),
      });
    }

    return schedule;
  } catch (error) {
    console.error('Amortization schedule calculation error:', error);
    return null;
  }
}

/**
 * تحويل النص إلى عنوان URL آمن ومحسن
 * Convert text to a safe and optimized URL slug
 */
export function createSlug(text: string): string {
  const sanitized = sanitizeText(text)
    .toLowerCase()
    // إزالة الحروف الخاصة مع الحفاظ على العربية والإنجليزية والأرقام والمسافات والشرطات
    .replace(/[^a-z0-9\u0600-\u06FF\s-]/g, '') 
    .trim();

  // استبدال المسافات المتعددة بشرطة واحدة
  const withDashes = sanitized.replace(/\s+/g, '-');

  // إزالة الشرطات المكررة
  const singleDashes = withDashes.replace(/-+/g, '-');

  // إزالة الشرطات من البداية والنهاية
  const finalSlug = singleDashes.replace(/^-+|-+$/g, '');

  return finalSlug;
}

/**
 * التحقق من دعم المتصفح للميزات
 * Check browser feature support
 */
export function checkBrowserSupport(): {
  webp: boolean;
  avif: boolean;
  localStorage: boolean;
  sessionStorage: boolean;
  intersectionObserver: boolean;
} {
  if (typeof window === 'undefined') {
    return {
      webp: false,
      avif: false,
      localStorage: false,
      sessionStorage: false,
      intersectionObserver: false,
    };
  }

  // التحقق من دعم WebP
  const checkWebP = (): boolean => {
    try {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      return canvas.toDataURL('image/webp').indexOf('image/webp') === 5;
    } catch {
      return false;
    }
  };

  // التحقق من دعم AVIF
  const checkAVIF = (): boolean => {
    try {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      return canvas.toDataURL('image/avif').indexOf('image/avif') === 5;
    } catch {
      return false;
    }
  };

  return {
    webp: checkWebP(),
    avif: checkAVIF(),
    localStorage: typeof window.localStorage !== 'undefined',
    sessionStorage: typeof window.sessionStorage !== 'undefined',
    intersectionObserver: typeof window.IntersectionObserver !== 'undefined',
  };
}

/**
 * حفظ البيانات في التخزين المحلي بطريقة آمنة
 * Save data to localStorage safely
 */
export function saveToLocalStorage(key: string, data: unknown): boolean {
  try {
    if (typeof window === 'undefined' || !window.localStorage) {
      return false;
    }

    const serializedData = JSON.stringify(data);
    window.localStorage.setItem(key, serializedData);
    return true;
  } catch (error) {
    console.warn('localStorage save failed:', error);
    return false;
  }
}

/**
 * استرداد البيانات من التخزين المحلي بطريقة آمنة
 * Retrieve data from localStorage safely
 */
export function getFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    if (typeof window === 'undefined' || !window.localStorage) {
      return defaultValue;
    }

    const item = window.localStorage.getItem(key);
    if (item === null) {
      return defaultValue;
    }

    return JSON.parse(item) as T;
  } catch (error) {
    console.warn('localStorage retrieve failed:', error);
    return defaultValue;
  }
}

/**
 * تنظيف التخزين المحلي
 * Clean up localStorage
 */
export function clearLocalStorage(pattern?: RegExp): boolean {
  try {
    if (typeof window === 'undefined' || !window.localStorage) {
      return false;
    }

    if (pattern) {
      const keys = Object.keys(window.localStorage);
      keys.forEach(key => {
        if (pattern.test(key)) {
          window.localStorage.removeItem(key);
        }
      });
    } else {
      window.localStorage.clear();
    }

    return true;
  } catch (error) {
    console.warn('localStorage clear failed:', error);
    return false;
  }
}
