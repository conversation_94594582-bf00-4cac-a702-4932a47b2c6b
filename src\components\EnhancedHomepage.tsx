"use client";

/**
 * مكون الصفحة الرئيسية المحسن مع أفضل الممارسات البرمجية
 * Enhanced homepage component with best practices
 */

import { useState, useEffect, useMemo, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import {
  Calculator,
  TrendingUp,
  Shield,
  BookOpen,
  HelpCircle,
  AlertTriangle,
  CheckCircle,
  Star,
  Users,
  Globe,
  ArrowRight
} from "lucide-react";
import LoanCalculatorForm from "./LoanCalculatorForm";
import LoanDurationCalculator from "./LoanDurationCalculator";
import AdSenseAd from "./ads/AdSenseAd";
import type { DictionaryKeys, Locale } from "@/types";
import { cn } from "@/lib/utils";

/**
 * خصائص مكون الصفحة الرئيسية
 * Homepage component props
 */
interface EnhancedHomepageProps {
  readonly dict: DictionaryKeys;
  readonly lang: Locale;
}

/**
 * بيانات الميزة
 * Feature data
 */
interface FeatureData {
  readonly icon: React.ComponentType<any>;
  readonly title: string;
  readonly description: string;
  readonly color: string;
}

/**
 * بيانات الإحصائية
 * Statistics data
 */
interface StatData {
  readonly number: string;
  readonly label: string;
  readonly icon?: React.ComponentType<any>;
}

/**
 * تعريف gtag للتحليلات
 * gtag declaration for analytics
 */
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}

/**
 * مكون الصفحة الرئيسية المحسن
 * Enhanced homepage component
 */
export default function EnhancedHomepage({ dict, lang }: EnhancedHomepageProps) {
  const [isVisible, setIsVisible] = useState(false);

  // تأثير الظهور التدريجي
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // بيانات الميزات مع التخزين المؤقت
  const features: FeatureData[] = useMemo(() => [
    {
      icon: Calculator,
      title: lang === 'ar' ? 'حاسبة دقيقة ومتقدمة' :
            lang === 'fr' ? 'Calculatrice précise et avancée' :
            'Accurate & Advanced Calculator',
      description: lang === 'ar' ? 'احسب القروض بدقة عالية مع دعم جميع العملات العربية' :
                   lang === 'fr' ? 'Calculez les prêts avec précision avec support de toutes les devises arabes' :
                   'Calculate loans with high precision supporting all Arab currencies',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Shield,
      title: lang === 'ar' ? 'تحذيرات شرعية واضحة' :
            lang === 'fr' ? 'Avertissements religieux clairs' :
            'Clear Religious Warnings',
      description: lang === 'ar' ? 'تحذيرات واضحة من مخاطر الربا مع البدائل الإسلامية' :
                   lang === 'fr' ? 'Avertissements clairs sur les risques de l\'intérêt avec alternatives islamiques' :
                   'Clear warnings about interest risks with Islamic alternatives',
      color: 'from-red-500 to-pink-500'
    },
    {
      icon: Globe,
      title: lang === 'ar' ? 'دعم شامل للعالم العربي' :
            lang === 'fr' ? 'Support complet du monde arabe' :
            'Complete Arab World Support',
      description: lang === 'ar' ? 'يدعم جميع العملات والبلدان العربية بواجهة محسنة' :
                   lang === 'fr' ? 'Supporte toutes les devises et pays arabes avec interface optimisée' :
                   'Supports all Arab currencies and countries with optimized interface',
      color: 'from-green-500 to-emerald-500'
    }
  ], [lang]);

  // بيانات الإحصائيات مع التخزين المؤقت
  const stats: StatData[] = useMemo(() => [
    {
      number: '22+',
      label: lang === 'ar' ? 'عملة عربية' :
             lang === 'fr' ? 'Devises arabes' :
             'Arab Currencies',
      icon: Globe
    },
    {
      number: '100%',
      label: lang === 'ar' ? 'مجاني تماماً' :
             lang === 'fr' ? 'Entièrement gratuit' :
             'Completely Free',
      icon: CheckCircle
    },
    {
      number: '24/7',
      label: lang === 'ar' ? 'متاح دائماً' :
             lang === 'fr' ? 'Toujours disponible' :
             'Always Available',
      icon: Shield
    }
  ], [lang]);

  /**
   * معالج النقر على زر العمل
   * CTA button click handler
   */
  const handleCTAClick = useCallback((action: string) => {
    try {
      // تتبع النقرات للتحليلات
      console.log(`CTA clicked: ${action}`);
      
      // يمكن إضافة منطق إضافي هنا مثل Google Analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'click', {
          event_category: 'engagement',
          event_label: action,
        });
      }
    } catch (error) {
      console.error('Error tracking CTA click:', error);
    }
  }, []);

  return (
    <div className={cn(
      "min-h-screen",
      lang === 'ar' && "font-arabic"
    )}>
      {/* Hero Section */}
      <section className={cn(
        "relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-primary/10 transition-all duration-1000",
        isVisible ? 'animate-in fade-in duration-1000' : 'opacity-0'
      )}>
        <div className="absolute inset-0 bg-grid-pattern opacity-5" />
        <div className="relative container mx-auto px-4 py-16">
          <div className="text-center max-w-4xl mx-auto">
            {/* Trust Badges */}
            <div className={cn(
              "flex justify-center gap-4 mb-8",
              lang === 'ar' && "flex-row-reverse"
            )}>
              <Badge variant="secondary" className={cn(
                "flex items-center gap-2 transition-all hover:scale-105",
                lang === 'ar' && "flex-row-reverse"
              )}>
                <Shield className="h-4 w-4" />
                <span className={lang === 'ar' ? 'font-arabic' : ''}>
                  {lang === 'ar' ? 'آمن ومجاني' :
                   lang === 'fr' ? 'Sûr et gratuit' :
                   'Safe & Free'}
                </span>
              </Badge>
              <Badge variant="secondary" className={cn(
                "flex items-center gap-2 transition-all hover:scale-105",
                lang === 'ar' && "flex-row-reverse"
              )}>
                <Users className="h-4 w-4" />
                <span className={lang === 'ar' ? 'font-arabic' : ''}>
                  {lang === 'ar' ? 'للعالم العربي' :
                   lang === 'fr' ? 'Pour le monde arabe' :
                   'For Arab World'}
                </span>
              </Badge>
            </div>

            {/* Main Headline */}
            <h1 className={cn(
              "text-4xl md:text-6xl font-bold text-primary mb-6 leading-tight animate-in slide-in-from-bottom-4 duration-700",
              lang === 'ar' && "font-arabic-display text-5xl md:text-7xl"
            )}>
              {dict.seo?.title || (
                lang === 'ar' ? 'حاسبة القروض الذكية' :
                lang === 'fr' ? 'Calculatrice de Prêts Intelligente' :
                'Smart Loan Calculator'
              )}
            </h1>

            {/* Subtitle */}
            <p className={cn(
              "text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed animate-in slide-in-from-bottom-8 duration-700 delay-150",
              lang === 'ar' && "text-2xl md:text-3xl font-arabic leading-loose"
            )}>
              {dict.seo?.description || (
                lang === 'ar' ? 'احسب قروضك بدقة مع تحذيرات شرعية وبدائل إسلامية آمنة' :
                lang === 'fr' ? 'Calculez vos prêts avec précision avec des avertissements religieux et des alternatives islamiques sûres' :
                'Calculate your loans accurately with religious warnings and safe Islamic alternatives'
              )}
            </p>

            {/* CTA Buttons */}
            <div className={cn(
              "flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-in slide-in-from-bottom-12 duration-700 delay-300",
              lang === 'ar' && "sm:flex-row-reverse"
            )}>
              <Button
                size="lg"
                className={cn(
                  "text-lg px-8 py-6 transition-all hover:scale-105 group",
                  lang === 'ar' && "font-arabic text-xl"
                )}
                asChild
                onClick={() => handleCTAClick('start_calculating')}
              >
                <Link href="#calculator">
                  <Calculator className={cn(
                    "h-5 w-5 transition-transform group-hover:rotate-12",
                    lang === 'ar' ? 'ml-2' : 'mr-2'
                  )} />
                  <span>
                    {lang === 'ar' ? 'ابدأ الحساب الآن' :
                     lang === 'fr' ? 'Commencer le calcul' :
                     'Start Calculating Now'}
                  </span>
                  <ArrowRight className={cn(
                    "h-4 w-4 ml-2 transition-transform group-hover:translate-x-1",
                    lang === 'ar' && "mr-2 ml-0 group-hover:-translate-x-1"
                  )} />
                </Link>
              </Button>
              <Button
                variant="outline"
                size="lg"
                className={cn(
                  "text-lg px-8 py-6 transition-all hover:scale-105",
                  lang === 'ar' && "font-arabic text-xl"
                )}
                asChild
                onClick={() => handleCTAClick('read_warnings')}
              >
                <Link href={`/${lang}/blog`}>
                  <BookOpen className={cn(
                    "h-5 w-5",
                    lang === 'ar' ? 'ml-2' : 'mr-2'
                  )} />
                  <span>
                    {lang === 'ar' ? 'اقرأ التحذيرات' :
                     lang === 'fr' ? 'Lire les avertissements' :
                     'Read Warnings'}
                  </span>
                </Link>
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto animate-in slide-in-from-bottom-16 duration-700 delay-500">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="text-center group hover:scale-105 transition-transform duration-300"
                >
                  {stat.icon && (
                    <stat.icon className={cn(
                      "h-8 w-8 mx-auto mb-3 text-primary/70 group-hover:text-primary transition-colors",
                      lang === 'ar' && "mb-4"
                    )} />
                  )}
                  <div className={cn(
                    "text-3xl md:text-4xl font-bold text-primary mb-2 group-hover:text-primary/90 transition-colors",
                    lang === 'ar' && "text-4xl md:text-5xl"
                  )}>
                    {stat.number}
                  </div>
                  <div className={cn(
                    "text-sm text-muted-foreground group-hover:text-foreground transition-colors",
                    lang === 'ar' && "font-arabic text-base"
                  )}>
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Header Ad */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <AdSenseAd
            slot="header-banner"
            className="w-full max-w-4xl mx-auto"
          />
        </div>
      </section>

      {/* Features Section */}
      <section className={cn(
        "py-16 bg-gradient-to-br from-background via-primary/5 to-background transition-all duration-1000",
        isVisible ? 'animate-in slide-in-from-bottom-8 duration-1000 delay-300' : 'opacity-0'
      )}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className={cn(
              "text-3xl md:text-4xl font-bold text-primary mb-4 animate-in slide-in-from-bottom-4 duration-700",
              lang === 'ar' && "font-arabic-display text-4xl md:text-5xl"
            )}>
              {lang === 'ar' ? 'لماذا تختار حاسبتنا؟' :
               lang === 'fr' ? 'Pourquoi choisir notre calculatrice?' :
               'Why Choose Our Calculator?'}
            </h2>
            <p className={cn(
              "text-xl text-muted-foreground max-w-2xl mx-auto animate-in slide-in-from-bottom-8 duration-700 delay-150",
              lang === 'ar' && "font-arabic text-2xl leading-relaxed"
            )}>
              {lang === 'ar' ? 'الأداة الوحيدة التي تجمع بين الدقة المالية والوعي الشرعي' :
               lang === 'fr' ? 'Le seul outil qui combine précision financière et conscience religieuse' :
               'The only tool that combines financial accuracy with religious awareness'}
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className={cn(
                  "text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group animate-in slide-in-from-bottom-12 duration-700",
                  `delay-${(index + 1) * 100}`,
                  lang === 'ar' && "font-arabic"
                )}
              >
                <CardHeader className="pb-4">
                  <div className={cn(
                    "mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 transition-all duration-300 group-hover:scale-110",
                    `bg-gradient-to-br ${feature.color} bg-opacity-10 group-hover:bg-opacity-20`
                  )}>
                    <feature.icon className="h-8 w-8 text-primary transition-transform duration-300 group-hover:scale-110" />
                  </div>
                  <CardTitle className={cn(
                    "text-xl transition-colors duration-300 group-hover:text-primary",
                    lang === 'ar' && "font-arabic-display text-2xl"
                  )}>
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className={cn(
                    "text-base leading-relaxed transition-colors duration-300 group-hover:text-foreground",
                    lang === 'ar' && "font-arabic text-lg"
                  )}>
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Warning Banner */}
      <section className={cn(
        "py-8 bg-gradient-to-r from-red-50 via-red-25 to-red-50 border-y border-red-200 transition-all duration-1000",
        isVisible ? 'animate-in slide-in-from-bottom-4 duration-1000 delay-600' : 'opacity-0'
      )}>
        <div className="container mx-auto px-4">
          <div className={cn(
            "flex items-center justify-center gap-4 text-center max-w-4xl mx-auto",
            lang === 'ar' && "flex-row-reverse"
          )}>
            <AlertTriangle className="h-8 w-8 text-red-600 flex-shrink-0 animate-pulse" />
            <div>
              <h3 className={cn(
                "text-xl font-bold text-red-800 mb-1",
                lang === 'ar' && "font-arabic-display text-2xl"
              )}>
                {lang === 'ar' ? 'تحذير مهم قبل الاقتراض' :
                 lang === 'fr' ? 'Avertissement important avant d\'emprunter' :
                 'Important Warning Before Borrowing'}
              </h3>
              <p className={cn(
                "text-red-700",
                lang === 'ar' && "font-arabic text-lg"
              )}>
                {lang === 'ar' ? 'تعرف على التكلفة الحقيقية والبدائل الشرعية قبل اتخاذ القرار' :
                 lang === 'fr' ? 'Connaissez le coût réel et les alternatives religieuses avant de décider' :
                 'Know the real cost and religious alternatives before deciding'}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Calculator Section */}
      <section id="calculator" className="py-16 bg-background">
        <LoanCalculatorForm dict={dict} lang={lang} />
      </section>

      {/* Content Ad */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <AdSenseAd
            slot="content-banner"
            className="w-full max-w-4xl mx-auto"
          />
        </div>
      </section>

      {/* Loan Duration Calculator Section */}
      <section className="py-16 bg-gradient-to-br from-blue-50 via-indigo-25 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className={cn(
              "text-3xl md:text-4xl font-bold text-primary mb-4",
              lang === 'ar' && "font-arabic-display text-4xl md:text-5xl"
            )}>
              {lang === 'ar' ? 'حاسبة مدة انتهاء القرض' :
               lang === 'fr' ? 'Calculatrice de durée de prêt' :
               'Loan Duration Calculator'}
            </h2>
            <p className={cn(
              "text-xl text-muted-foreground max-w-2xl mx-auto",
              lang === 'ar' && "font-arabic text-2xl leading-relaxed"
            )}>
              {lang === 'ar' ? 'أدخل القسط الشهري المرغوب واعرف متى سينتهي قرضك' :
               lang === 'fr' ? 'Entrez le paiement mensuel souhaité et découvrez quand votre prêt se terminera' :
               'Enter your desired monthly payment and find out when your loan will end'}
            </p>
          </div>
          <LoanDurationCalculator />
        </div>
      </section>

      {/* Trust Section */}
      <section className={cn(
        "py-16 bg-gradient-to-br from-green-50 via-green-25 to-green-50 transition-all duration-1000",
        isVisible ? 'animate-in slide-in-from-bottom-8 duration-1000 delay-900' : 'opacity-0'
      )}>
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <div className="flex justify-center mb-6">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>
            </div>
            <h2 className={cn(
              "text-3xl font-bold text-green-800 mb-4",
              lang === 'ar' && "font-arabic-display text-4xl"
            )}>
              {lang === 'ar' ? 'أداة موثوقة ومجانية تماماً' :
               lang === 'fr' ? 'Outil fiable et entièrement gratuit' :
               'Trusted & Completely Free Tool'}
            </h2>
            <p className={cn(
              "text-lg text-green-700 mb-8 leading-relaxed",
              lang === 'ar' && "font-arabic text-xl"
            )}>
              {lang === 'ar' ? 'لا نطلب معلومات شخصية، لا رسوم خفية، لا التزامات. فقط حسابات دقيقة وتحذيرات مفيدة لمساعدتك في اتخاذ قرارات مالية حكيمة.' :
               lang === 'fr' ? 'Aucune information personnelle requise, aucun frais caché, aucun engagement. Juste des calculs précis et des avertissements utiles pour vous aider à prendre des décisions financières sages.' :
               'No personal information required, no hidden fees, no commitments. Just accurate calculations and helpful warnings to help you make wise financial decisions.'}
            </p>
            <div className={cn(
              "flex flex-wrap justify-center gap-4",
              lang === 'ar' && "flex-row-reverse"
            )}>
              <Badge variant="outline" className={cn(
                "flex items-center gap-2 px-4 py-2 transition-all hover:scale-105 hover:bg-green-100",
                lang === 'ar' && "flex-row-reverse"
              )}>
                <Shield className="h-4 w-4" />
                <span className={lang === 'ar' ? 'font-arabic' : ''}>
                  {lang === 'ar' ? 'خصوصية محمية' :
                   lang === 'fr' ? 'Confidentialité protégée' :
                   'Privacy Protected'}
                </span>
              </Badge>
              <Badge variant="outline" className={cn(
                "flex items-center gap-2 px-4 py-2 transition-all hover:scale-105 hover:bg-green-100",
                lang === 'ar' && "flex-row-reverse"
              )}>
                <Star className="h-4 w-4" />
                <span className={lang === 'ar' ? 'font-arabic' : ''}>
                  {lang === 'ar' ? 'دقة عالية' :
                   lang === 'fr' ? 'Haute précision' :
                   'High Accuracy'}
                </span>
              </Badge>
              <Badge variant="outline" className={cn(
                "flex items-center gap-2 px-4 py-2 transition-all hover:scale-105 hover:bg-green-100",
                lang === 'ar' && "flex-row-reverse"
              )}>
                <Globe className="h-4 w-4" />
                <span className={lang === 'ar' ? 'font-arabic' : ''}>
                  {lang === 'ar' ? 'جميع العملات العربية' :
                   lang === 'fr' ? 'Toutes devises arabes' :
                   'All Arab Currencies'}
                </span>
              </Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className={cn(
        "py-16 bg-gradient-to-r from-primary via-primary/90 to-primary text-primary-foreground transition-all duration-1000",
        isVisible ? 'animate-in slide-in-from-bottom-12 duration-1000 delay-1200' : 'opacity-0'
      )}>
        <div className="container mx-auto px-4 text-center">
          <h2 className={cn(
            "text-3xl md:text-4xl font-bold mb-4",
            lang === 'ar' && "font-arabic-display text-4xl md:text-5xl"
          )}>
            {lang === 'ar' ? 'ابدأ الآن واتخذ قراراً مدروساً' :
             lang === 'fr' ? 'Commencez maintenant et prenez une décision éclairée' :
             'Start Now and Make an Informed Decision'}
          </h2>
          <p className={cn(
            "text-xl mb-8 opacity-90 max-w-2xl mx-auto",
            lang === 'ar' && "font-arabic text-2xl leading-relaxed"
          )}>
            {lang === 'ar' ? 'استخدم حاسبتنا المجانية لفهم التكلفة الحقيقية واكتشاف البدائل الآمنة' :
             lang === 'fr' ? 'Utilisez notre calculatrice gratuite pour comprendre le coût réel et découvrir des alternatives sûres' :
             'Use our free calculator to understand the real cost and discover safe alternatives'}
          </p>
          <div className={cn(
            "flex flex-col sm:flex-row gap-4 justify-center",
            lang === 'ar' && "sm:flex-row-reverse"
          )}>
            <Button
              size="lg"
              variant="secondary"
              className={cn(
                "text-lg px-8 py-6 transition-all hover:scale-105 group",
                lang === 'ar' && "font-arabic text-xl"
              )}
              asChild
              onClick={() => handleCTAClick('final_calculate')}
            >
              <Link href="#calculator">
                <Calculator className={cn(
                  "h-5 w-5 transition-transform group-hover:rotate-12",
                  lang === 'ar' ? 'ml-2' : 'mr-2'
                )} />
                <span>
                  {lang === 'ar' ? 'احسب الآن' :
                   lang === 'fr' ? 'Calculer maintenant' :
                   'Calculate Now'}
                </span>
              </Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className={cn(
                "text-lg px-8 py-6 border-primary-foreground bg-transparent text-primary-foreground hover:bg-primary-foreground hover:text-primary transition-all hover:scale-105",
                lang === 'ar' && "font-arabic text-xl"
              )}
              asChild
              onClick={() => handleCTAClick('view_faq')}
            >
              <Link href={`/${lang}/faq`}>
                <HelpCircle className={cn(
                  "h-5 w-5",
                  lang === 'ar' ? 'ml-2' : 'mr-2'
                )} />
                <span>
                  {lang === 'ar' ? 'أسئلة شائعة' :
                   lang === 'fr' ? 'Questions fréquentes' :
                   'FAQ'}
                </span>
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}