"use client";

/**
 * مكون محدد اللغة المحسن مع أفضل الممارسات البرمجية
 * Enhanced language switcher component with best practices
 */

import { usePathname } from "next/navigation";
import Link from "next/link";
import { useState, useCallback, useMemo } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Globe, Check } from "lucide-react";
import type { Locale } from "@/types";
import { cn } from "@/lib/utils";

/**
 * خصائص مكون محدد اللغة
 * Language switcher component props
 */
interface LanguageSwitcherProps {
  readonly currentLang?: Locale;
  readonly className?: string;
  readonly size?: "sm" | "default" | "lg" | "icon";
  readonly variant?: "default" | "outline" | "ghost" | "secondary";
  readonly showCurrentLang?: boolean;
}

/**
 * بيانات اللغة
 * Language data
 */
interface LanguageData {
  readonly code: Locale;
  readonly name: string;
  readonly nativeName: string;
  readonly flag: string;
  readonly dir: "ltr" | "rtl";
}

/**
 * قائمة اللغات المدعومة
 * Supported languages list
 */
const SUPPORTED_LANGUAGES: readonly LanguageData[] = [
  {
    code: "en",
    name: "English",
    nativeName: "English",
    flag: "🇺🇸",
    dir: "ltr",
  },
  {
    code: "fr",
    name: "French",
    nativeName: "Français",
    flag: "🇫🇷",
    dir: "ltr",
  },
  {
    code: "ar",
    name: "Arabic",
    nativeName: "العربية",
    flag: "🇸🇦",
    dir: "rtl",
  },
] as const;

/**
 * مكون محدد اللغة الرئيسي
 * Main language switcher component
 */
export default function LanguageSwitcher({
  currentLang,
  className = "",
  size = "icon",
  variant = "outline",
  showCurrentLang = false,
}: LanguageSwitcherProps) {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  // تحديد اللغة الحالية من المسار
  const detectedCurrentLang = useMemo((): Locale => {
    if (currentLang) return currentLang;
    
    if (!pathname) return "en";
    
    const segments = pathname.split("/");
    const langFromPath = segments[1] as Locale;
    
    return SUPPORTED_LANGUAGES.some(lang => lang.code === langFromPath)
      ? langFromPath
      : "en";
  }, [currentLang, pathname]);

  // الحصول على بيانات اللغة الحالية مع ضمان عدم وجود undefined
  const currentLanguage = useMemo((): LanguageData => {
    const found = SUPPORTED_LANGUAGES.find(lang => lang.code === detectedCurrentLang);
    if (found) return found;
    
    // قيمة افتراضية في حالة عدم وجود اللغة
    return {
      code: "en",
      name: "English",
      nativeName: "English",
      flag: "🇺🇸",
      dir: "ltr",
    };
  }, [detectedCurrentLang]);

  /**
   * إنشاء مسار جديد للغة المحددة
   * Create new path for selected language
   */
  const createLocalizedPath = useCallback((locale: Locale): string => {
    if (!pathname) return `/${locale}`;
    
    const segments = pathname.split("/").filter(Boolean);
    
    // استبدال اللغة الحالية أو إضافة جديدة
    if (SUPPORTED_LANGUAGES.some(lang => lang.code === segments[0])) {
      segments[0] = locale;
    } else {
      segments.unshift(locale);
    }
    
    return `/${segments.join("/")}`;
  }, [pathname]);

  /**
   * معالج إغلاق القائمة
   * Close handler
   */
  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  /**
   * معالج تغيير اللغة
   * Language change handler
   */
  const handleLanguageChange = useCallback((locale: Locale) => {
    try {
      // إغلاق القائمة أولاً
      handleClose();
      
      // يمكن إضافة منطق إضافي هنا مثل تخزين اللغة المختارة
      console.log(`Language changed to: ${locale}`);
    } catch (error) {
      console.error("Error changing language:", error);
    }
  }, [handleClose]);

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn(
            "transition-all duration-200",
            size === "icon" ? "h-9 w-9" : showCurrentLang && "gap-2",
            className
          )}
          aria-label={`Change language. Current: ${currentLanguage.nativeName}`}
        >
          <Globe className={cn(
            "transition-transform duration-200",
            size === "sm" ? "h-4 w-4" : "h-[1.2rem] w-[1.2rem]",
            isOpen && "rotate-12"
          )} />
          
          {showCurrentLang && size !== "icon" && (
            <>
              <span className="text-sm font-medium">
                {currentLanguage.flag} {currentLanguage.nativeName}
              </span>
              <Badge variant="secondary" className="text-xs">
                {currentLanguage.code.toUpperCase()}
              </Badge>
            </>
          )}
          
          <span className="sr-only">
            {currentLanguage.code === "ar"
              ? "تغيير اللغة"
              : currentLanguage.code === "fr"
              ? "Changer la langue"
              : "Change language"
            }
          </span>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent
        align="end"
        className="w-48 p-1"
        side="bottom"
        sideOffset={4}
      >
        {SUPPORTED_LANGUAGES.map((language) => {
          const isSelected = language.code === detectedCurrentLang;
          
          return (
            <DropdownMenuItem key={language.code} asChild>
              <Link
                href={createLocalizedPath(language.code)}
                onClick={() => handleLanguageChange(language.code)}
                className={cn(
                  "flex items-center gap-3 w-full px-3 py-2 rounded-sm transition-colors",
                  "hover:bg-accent focus:bg-accent cursor-pointer",
                  isSelected && "bg-accent/50",
                  language.dir === "rtl" && "flex-row-reverse text-right"
                )}
                dir={language.dir}
              >
                <span className="text-lg" role="img" aria-label={`${language.name} flag`}>
                  {language.flag}
                </span>
                
                <div className="flex-1 min-w-0">
                  <div className={cn(
                    "flex items-center gap-2",
                    language.dir === "rtl" && "flex-row-reverse"
                  )}>
                    <span className={cn(
                      "font-medium text-sm",
                      language.code === "ar" && "font-arabic"
                    )}>
                      {language.nativeName}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {language.code.toUpperCase()}
                    </Badge>
                  </div>
                  
                  <div className={cn(
                    "text-xs text-muted-foreground",
                    language.code === "ar" && "font-arabic"
                  )}>
                    {language.name}
                  </div>
                </div>
                
                {isSelected && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </Link>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
